import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/user_models.dart';
import '../services/api/user_permissions_api_service.dart';

/// متحكم صلاحيات المستخدمين
class UserPermissionsController extends GetxController {
  final UserPermissionsApiService _apiService = UserPermissionsApiService();

  // قوائم الصلاحيات
  final RxList<UserPermission> _allPermissions = <UserPermission>[].obs;
  final RxList<UserPermission> _filteredPermissions = <UserPermission>[].obs;
  final RxList<UserPermission> _userPermissions = <UserPermission>[].obs;

  // الصلاحية الحالية
  final Rx<UserPermission?> _currentPermission = Rx<UserPermission?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<int?> _userFilter = Rx<int?>(null);
  final Rx<String?> _permissionFilter = Rx<String?>(null);
  final Rx<String?> _moduleFilter = Rx<String?>(null);
  final RxBool _showActiveOnly = true.obs;

  // إحصائيات
  final RxInt _totalPermissions = 0.obs;
  final RxInt _activePermissions = 0.obs;
  final RxInt _revokedPermissions = 0.obs;

  // Getters
  List<UserPermission> get allPermissions => _allPermissions;
  List<UserPermission> get filteredPermissions => _filteredPermissions;
  List<UserPermission> get userPermissions => _userPermissions;
  UserPermission? get currentPermission => _currentPermission.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  int? get userFilter => _userFilter.value;
  String? get permissionFilter => _permissionFilter.value;
  String? get moduleFilter => _moduleFilter.value;
  bool get showActiveOnly => _showActiveOnly.value;
  int get totalPermissions => _totalPermissions.value;
  int get activePermissions => _activePermissions.value;
  int get revokedPermissions => _revokedPermissions.value;

  @override
  void onInit() {
    super.onInit();
    loadAllPermissions();
    loadStatistics();
  }

  /// تحميل جميع صلاحيات المستخدمين
  Future<void> loadAllPermissions() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final permissions = await _apiService.getAllPermissions();
      _allPermissions.assignAll(permissions);
      _applyFilters();
      debugPrint('تم تحميل ${permissions.length} صلاحية مستخدم');
    } catch (e) {
      _error.value = 'خطأ في تحميل صلاحيات المستخدمين: $e';
      debugPrint('خطأ في تحميل صلاحيات المستخدمين: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل الإحصائيات
  Future<void> loadStatistics() async {
    try {
      final stats = await _apiService.getStatistics();
      _totalPermissions.value = stats['total'] ?? 0;
      _activePermissions.value = stats['active'] ?? 0;
      _revokedPermissions.value = stats['revoked'] ?? 0;
      debugPrint('تم تحميل إحصائيات صلاحيات المستخدمين');
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات صلاحيات المستخدمين: $e');
    }
  }

  /// الحصول على صلاحية مستخدم بالمعرف
  Future<void> getPermissionById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final permission = await _apiService.getPermissionById(id);
      _currentPermission.value = permission;
      debugPrint('تم تحميل صلاحية المستخدم');
    } catch (e) {
      _error.value = 'خطأ في تحميل صلاحية المستخدم: $e';
      debugPrint('خطأ في تحميل صلاحية المستخدم: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// منح صلاحية لمستخدم
  Future<bool> grantPermission(int userId, String permissionName, String module) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final permission = await _apiService.grantPermission(userId, permissionName, module);
      _allPermissions.add(permission);
      _applyFilters();
      await loadStatistics();
      debugPrint('تم منح صلاحية للمستخدم: $permissionName');
      return true;
    } catch (e) {
      _error.value = 'خطأ في منح الصلاحية للمستخدم: $e';
      debugPrint('خطأ في منح الصلاحية للمستخدم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إلغاء صلاحية من مستخدم
  Future<bool> revokePermission(int permissionId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.revokePermission(permissionId);
      final index = _allPermissions.indexWhere((p) => p.id == permissionId);
      if (index != -1) {
        _allPermissions[index] = _allPermissions[index].copyWith(isActive: false);
        _applyFilters();
      }
      await loadStatistics();
      debugPrint('تم إلغاء الصلاحية من المستخدم');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إلغاء الصلاحية من المستخدم: $e';
      debugPrint('خطأ في إلغاء الصلاحية من المستخدم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على صلاحيات مستخدم محدد
  Future<void> getUserPermissions(int userId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final permissions = await _apiService.getUserPermissions(userId);
      _userPermissions.assignAll(permissions);
      debugPrint('تم تحميل ${permissions.length} صلاحية للمستخدم $userId');
    } catch (e) {
      _error.value = 'خطأ في تحميل صلاحيات المستخدم: $e';
      debugPrint('خطأ في تحميل صلاحيات المستخدم: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// التحقق من صلاحية مستخدم
  Future<bool> checkUserPermission(int userId, String permissionName, String module) async {
    try {
      final hasPermission = await _apiService.checkUserPermission(userId, permissionName, module);
      debugPrint('التحقق من صلاحية المستخدم $userId للصلاحية $permissionName: $hasPermission');
      return hasPermission;
    } catch (e) {
      debugPrint('خطأ في التحقق من صلاحية المستخدم: $e');
      return false;
    }
  }

  /// منح صلاحيات متعددة لمستخدم
  Future<bool> grantMultiplePermissions(int userId, List<Map<String, String>> permissions) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final grantedPermissions = await _apiService.grantMultiplePermissions(userId, permissions);
      _allPermissions.addAll(grantedPermissions);
      _applyFilters();
      await loadStatistics();
      debugPrint('تم منح ${grantedPermissions.length} صلاحية للمستخدم');
      return true;
    } catch (e) {
      _error.value = 'خطأ في منح الصلاحيات المتعددة: $e';
      debugPrint('خطأ في منح الصلاحيات المتعددة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إلغاء جميع صلاحيات مستخدم
  Future<bool> revokeAllUserPermissions(int userId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.revokeAllUserPermissions(userId);
      for (int i = 0; i < _allPermissions.length; i++) {
        if (_allPermissions[i].userId == userId) {
          _allPermissions[i] = _allPermissions[i].copyWith(isActive: false);
        }
      }
      _applyFilters();
      await loadStatistics();
      debugPrint('تم إلغاء جميع صلاحيات المستخدم');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إلغاء جميع صلاحيات المستخدم: $e';
      debugPrint('خطأ في إلغاء جميع صلاحيات المستخدم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// نسخ صلاحيات من مستخدم إلى آخر
  Future<bool> copyPermissions(int fromUserId, int toUserId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final copiedPermissions = await _apiService.copyPermissions(fromUserId, toUserId);
      _allPermissions.addAll(copiedPermissions);
      _applyFilters();
      await loadStatistics();
      debugPrint('تم نسخ ${copiedPermissions.length} صلاحية من المستخدم $fromUserId إلى $toUserId');
      return true;
    } catch (e) {
      _error.value = 'خطأ في نسخ الصلاحيات: $e';
      debugPrint('خطأ في نسخ الصلاحيات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على المستخدمين حسب الصلاحية
  Future<List<Map<String, dynamic>>?> getUsersByPermission(String permissionName, String module) async {
    try {
      final users = await _apiService.getUsersByPermission(permissionName, module);
      debugPrint('تم تحميل ${users.length} مستخدم للصلاحية $permissionName');
      return users;
    } catch (e) {
      debugPrint('خطأ في تحميل المستخدمين حسب الصلاحية: $e');
      return null;
    }
  }

  /// تصدير تقرير الصلاحيات
  Future<String?> exportPermissionsReport(String format) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final filePath = await _apiService.exportPermissionsReport(format);
      debugPrint('تم تصدير تقرير الصلاحيات: $filePath');
      return filePath;
    } catch (e) {
      _error.value = 'خطأ في تصدير تقرير الصلاحيات: $e';
      debugPrint('خطأ في تصدير تقرير الصلاحيات: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allPermissions.where((permission) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!permission.permissionName.toLowerCase().contains(query) &&
            !permission.module.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح المستخدم
      if (_userFilter.value != null && permission.userId != _userFilter.value) {
        return false;
      }

      // مرشح الصلاحية
      if (_permissionFilter.value != null && permission.permissionName != _permissionFilter.value) {
        return false;
      }

      // مرشح الوحدة
      if (_moduleFilter.value != null && permission.module != _moduleFilter.value) {
        return false;
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && !permission.isActive) {
        return false;
      }

      return true;
    }).toList();

    _filteredPermissions.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح المستخدم
  void setUserFilter(int? userId) {
    _userFilter.value = userId;
    _applyFilters();
  }

  /// تعيين مرشح الصلاحية
  void setPermissionFilter(String? permission) {
    _permissionFilter.value = permission;
    _applyFilters();
  }

  /// تعيين مرشح الوحدة
  void setModuleFilter(String? module) {
    _moduleFilter.value = module;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _userFilter.value = null;
    _permissionFilter.value = null;
    _moduleFilter.value = null;
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await Future.wait([
      loadAllPermissions(),
      loadStatistics(),
    ]);
  }
}
