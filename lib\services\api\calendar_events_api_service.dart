import 'package:flutter/foundation.dart';
import '../../models/calendar_models.dart';
import 'api_service.dart';

/// خدمة API لأحداث التقويم - متطابقة مع ASP.NET Core API
class CalendarEventsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع أحداث التقويم
  Future<List<CalendarEvent>> getAllEvents() async {
    try {
      final response = await _apiService.get('/CalendarEvents');
      return _apiService.handleListResponse<CalendarEvent>(
        response,
        (json) => CalendarEvent.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أحداث التقويم: $e');
      rethrow;
    }
  }

  /// الحصول على حدث تقويم بواسطة المعرف
  Future<CalendarEvent?> getEventById(int id) async {
    try {
      final response = await _apiService.get('/CalendarEvents/$id');
      return _apiService.handleResponse<CalendarEvent>(
        response,
        (json) => CalendarEvent.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على حدث التقويم: $e');
      return null;
    }
  }

  /// إنشاء حدث تقويم جديد
  Future<CalendarEvent?> createEvent(CalendarEvent event) async {
    try {
      final response = await _apiService.post(
        '/CalendarEvents',
        body: event.toJson(),
      );
      return _apiService.handleResponse<CalendarEvent>(
        response,
        (json) => CalendarEvent.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء حدث التقويم: $e');
      rethrow;
    }
  }

  /// تحديث حدث تقويم
  Future<CalendarEvent?> updateEvent(CalendarEvent event) async {
    try {
      final response = await _apiService.put(
        '/CalendarEvents/${event.id}',
        body: event.toJson(),
      );
      return _apiService.handleResponse<CalendarEvent>(
        response,
        (json) => CalendarEvent.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث حدث التقويم: $e');
      rethrow;
    }
  }

  /// حذف حدث تقويم
  Future<bool> deleteEvent(int id) async {
    try {
      final response = await _apiService.delete('/CalendarEvents/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف حدث التقويم: $e');
      return false;
    }
  }

  /// الحصول على أحداث في فترة زمنية
  Future<List<CalendarEvent>> getEventsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final startTimestamp = startDate.millisecondsSinceEpoch ~/ 1000;
      final endTimestamp = endDate.millisecondsSinceEpoch ~/ 1000;
      
      final response = await _apiService.get(
        '/CalendarEvents/date-range?startDate=$startTimestamp&endDate=$endTimestamp'
      );
      return _apiService.handleListResponse<CalendarEvent>(
        response,
        (json) => CalendarEvent.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أحداث الفترة الزمنية: $e');
      rethrow;
    }
  }

  /// الحصول على أحداث مستخدم معين
  Future<List<CalendarEvent>> getEventsByUser(int userId) async {
    try {
      final response = await _apiService.get('/CalendarEvents/user/$userId');
      return _apiService.handleListResponse<CalendarEvent>(
        response,
        (json) => CalendarEvent.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أحداث المستخدم: $e');
      rethrow;
    }
  }

  /// الحصول على أحداث مشروع معين
  Future<List<CalendarEvent>> getEventsByProject(int projectId) async {
    try {
      final response = await _apiService.get('/CalendarEvents/project/$projectId');
      return _apiService.handleListResponse<CalendarEvent>(
        response,
        (json) => CalendarEvent.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أحداث المشروع: $e');
      rethrow;
    }
  }

  /// الحصول على أحداث مهمة معينة
  Future<List<CalendarEvent>> getEventsByTask(int taskId) async {
    try {
      final response = await _apiService.get('/CalendarEvents/task/$taskId');
      return _apiService.handleListResponse<CalendarEvent>(
        response,
        (json) => CalendarEvent.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أحداث المهمة: $e');
      rethrow;
    }
  }

  /// البحث في أحداث التقويم
  Future<List<CalendarEvent>> searchEvents(String query) async {
    try {
      final response = await _apiService.get('/CalendarEvents/search?searchTerm=$query');
      return _apiService.handleListResponse<CalendarEvent>(
        response,
        (json) => CalendarEvent.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن أحداث التقويم: $e');
      rethrow;
    }
  }

  /// الحصول على الأحداث القادمة
  Future<List<CalendarEvent>> getUpcomingEvents({int limit = 10}) async {
    try {
      final response = await _apiService.get('/CalendarEvents/upcoming?limit=$limit');
      return _apiService.handleListResponse<CalendarEvent>(
        response,
        (json) => CalendarEvent.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأحداث القادمة: $e');
      rethrow;
    }
  }

  /// الحصول على أحداث اليوم
  Future<List<CalendarEvent>> getTodayEvents() async {
    try {
      final response = await _apiService.get('/CalendarEvents/today');
      return _apiService.handleListResponse<CalendarEvent>(
        response,
        (json) => CalendarEvent.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أحداث اليوم: $e');
      rethrow;
    }
  }

  /// الحصول على أحداث هذا الأسبوع
  Future<List<CalendarEvent>> getThisWeekEvents() async {
    try {
      final response = await _apiService.get('/CalendarEvents/this-week');
      return _apiService.handleListResponse<CalendarEvent>(
        response,
        (json) => CalendarEvent.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أحداث هذا الأسبوع: $e');
      rethrow;
    }
  }

  /// الحصول على أحداث هذا الشهر
  Future<List<CalendarEvent>> getThisMonthEvents() async {
    try {
      final response = await _apiService.get('/CalendarEvents/this-month');
      return _apiService.handleListResponse<CalendarEvent>(
        response,
        (json) => CalendarEvent.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أحداث هذا الشهر: $e');
      rethrow;
    }
  }

  /// الحصول على الأحداث المتكررة
  Future<List<CalendarEvent>> getRecurringEvents() async {
    try {
      final response = await _apiService.get('/CalendarEvents/recurring');
      return _apiService.handleListResponse<CalendarEvent>(
        response,
        (json) => CalendarEvent.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأحداث المتكررة: $e');
      rethrow;
    }
  }

  /// إنشاء حدث متكرر
  Future<List<CalendarEvent>> createRecurringEvent(
    CalendarEvent baseEvent,
    String recurrencePattern,
    DateTime endDate,
  ) async {
    try {
      final response = await _apiService.post(
        '/CalendarEvents/recurring',
        body: {
          ...baseEvent.toJson(),
          'recurrencePattern': recurrencePattern,
          'recurrenceEndDate': endDate.millisecondsSinceEpoch ~/ 1000,
        },
      );
      return _apiService.handleListResponse<CalendarEvent>(
        response,
        (json) => CalendarEvent.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء الحدث المتكرر: $e');
      rethrow;
    }
  }

  /// تحديث حدث متكرر
  Future<bool> updateRecurringEvent(
    int eventId,
    CalendarEvent updatedEvent,
    bool updateAll,
  ) async {
    try {
      final response = await _apiService.put(
        '/CalendarEvents/$eventId/recurring',
        body: {
          ...updatedEvent.toJson(),
          'updateAll': updateAll,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث الحدث المتكرر: $e');
      return false;
    }
  }

  /// حذف حدث متكرر
  Future<bool> deleteRecurringEvent(int eventId, bool deleteAll) async {
    try {
      final response = await _apiService.delete(
        '/CalendarEvents/$eventId/recurring?deleteAll=$deleteAll'
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف الحدث المتكرر: $e');
      return false;
    }
  }

  /// إضافة مشارك لحدث
  Future<bool> addEventParticipant(int eventId, int userId) async {
    try {
      final response = await _apiService.post(
        '/CalendarEvents/$eventId/participants',
        body: {'userId': userId},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إضافة مشارك للحدث: $e');
      return false;
    }
  }

  /// إزالة مشارك من حدث
  Future<bool> removeEventParticipant(int eventId, int userId) async {
    try {
      final response = await _apiService.delete('/CalendarEvents/$eventId/participants/$userId');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إزالة مشارك من الحدث: $e');
      return false;
    }
  }

  /// الحصول على مشاركي حدث
  Future<List<Map<String, dynamic>>> getEventParticipants(int eventId) async {
    try {
      final response = await _apiService.get('/CalendarEvents/$eventId/participants');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return List<Map<String, dynamic>>.from(response.body as List);
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على مشاركي الحدث: $e');
      return [];
    }
  }

  /// تحديث حالة حضور مشارك
  Future<bool> updateParticipantStatus(
    int eventId,
    int userId,
    String status,
  ) async {
    try {
      final response = await _apiService.put(
        '/CalendarEvents/$eventId/participants/$userId/status',
        body: {'status': status},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة حضور المشارك: $e');
      return false;
    }
  }

  /// إرسال تذكير بحدث
  Future<bool> sendEventReminder(int eventId) async {
    try {
      final response = await _apiService.post(
        '/CalendarEvents/$eventId/reminder',
        body: {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إرسال تذكير الحدث: $e');
      return false;
    }
  }

  /// تصدير أحداث التقويم
  Future<String?> exportEvents(
    String format,
    DateTime? startDate,
    DateTime? endDate,
  ) async {
    try {
      var url = '/CalendarEvents/export?format=$format';
      if (startDate != null && endDate != null) {
        final startTimestamp = startDate.millisecondsSinceEpoch ~/ 1000;
        final endTimestamp = endDate.millisecondsSinceEpoch ~/ 1000;
        url += '&startDate=$startTimestamp&endDate=$endTimestamp';
      }
      
      final response = await _apiService.get(url);
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تصدير أحداث التقويم: $e');
      return null;
    }
  }

  /// استيراد أحداث التقويم
  Future<bool> importEvents(String data, String format) async {
    try {
      final response = await _apiService.post(
        '/CalendarEvents/import',
        body: {
          'data': data,
          'format': format,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في استيراد أحداث التقويم: $e');
      return false;
    }
  }
}
