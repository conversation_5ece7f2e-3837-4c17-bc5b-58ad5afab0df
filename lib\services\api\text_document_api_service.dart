import 'package:flutter/foundation.dart';
import '../../models/text_document_model.dart';
import 'api_service.dart';

/// خدمة API للمستندات النصية - متطابقة مع ASP.NET Core API
class TextDocumentApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع المستندات
  Future<List<TextDocument>> getAllDocuments() async {
    try {
      final response = await _apiService.get('/TextDocuments');
      return _apiService.handleListResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل المستندات: $e');
      rethrow;
    }
  }

  /// الحصول على مستند بواسطة المعرف
  Future<TextDocument?> getDocumentById(int id) async {
    try {
      final response = await _apiService.get('/TextDocuments/$id');
      return _apiService.handleResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل المستند $id: $e');
      return null;
    }
  }

  /// الحصول على المستندات المرتبطة بمهمة
  Future<List<TextDocument>> getDocumentsByTaskId(int taskId) async {
    try {
      final response = await _apiService.get('/TextDocuments/task/$taskId');
      return _apiService.handleListResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل مستندات المهمة $taskId: $e');
      return [];
    }
  }

  /// البحث في المستندات
  Future<List<TextDocument>> searchDocuments(String query) async {
    try {
      final response = await _apiService.get('/TextDocuments/search?searchTerm=$query');
      return _apiService.handleListResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن المستندات: $e');
      return [];
    }
  }

  /// إنشاء مستند جديد
  Future<TextDocument?> createDocument(TextDocument document) async {
    try {
      final response = await _apiService.post(
        '/TextDocuments',
        body: document.toJson(),
      );
      return _apiService.handleResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء المستند: $e');
      rethrow;
    }
  }

  /// تحديث مستند
  Future<TextDocument?> updateDocument(TextDocument document) async {
    try {
      final response = await _apiService.put(
        '/TextDocuments/${document.id}',
        body: document.toJson(),
      );
      return _apiService.handleResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث المستند ${document.id}: $e');
      rethrow;
    }
  }

  /// حذف مستند
  Future<bool> deleteDocument(int id) async {
    try {
      final response = await _apiService.delete('/TextDocuments/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف المستند $id: $e');
      return false;
    }
  }

  /// مشاركة مستند
  Future<bool> shareDocument(int id, bool isShared) async {
    try {
      final response = await _apiService.put(
        '/TextDocuments/$id/share',
        body: {'isShared': isShared},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في مشاركة المستند $id: $e');
      return false;
    }
  }

  /// الحصول على المستندات المشتركة
  Future<List<TextDocument>> getSharedDocuments() async {
    try {
      final response = await _apiService.get('/TextDocuments/shared');
      return _apiService.handleListResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل المستندات المشتركة: $e');
      return [];
    }
  }

  /// الحصول على المستندات حسب النوع
  Future<List<TextDocument>> getDocumentsByType(String type) async {
    try {
      final response = await _apiService.get('/TextDocuments/type/$type');
      return _apiService.handleListResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل مستندات النوع $type: $e');
      return [];
    }
  }

  /// الحصول على المستندات الحديثة
  Future<List<TextDocument>> getRecentDocuments({int limit = 10}) async {
    try {
      final response = await _apiService.get('/TextDocuments/recent?limit=$limit');
      return _apiService.handleListResponse<TextDocument>(
        response,
        (json) => TextDocument.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل المستندات الحديثة: $e');
      return [];
    }
  }

  /// تصدير مستند
  Future<String?> exportDocument(int id, String format) async {
    try {
      final response = await _apiService.get('/TextDocuments/$id/export?format=$format');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تصدير المستند $id: $e');
      return null;
    }
  }
}
