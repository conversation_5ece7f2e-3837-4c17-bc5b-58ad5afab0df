import 'package:flutter/foundation.dart';

import 'api_service.dart';

/// خدمة API لسجلات الأنشطة - متطابقة مع ASP.NET Core API
class ActivityLogsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع سجلات الأنشطة
  Future<List<ActivityLog>> getAllActivityLogs() async {
    try {
      final response = await _apiService.get('/ActivityLogs');
      return _apiService.handleListResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات الأنشطة: $e');
      rethrow;
    }
  }

  /// الحصول على سجل نشاط بواسطة المعرف
  Future<ActivityLog?> getActivityLogById(int id) async {
    try {
      final response = await _apiService.get('/ActivityLogs/$id');
      return _apiService.handleResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجل النشاط $id: $e');
      return null;
    }
  }

  /// الحصول على سجلات أنشطة مستخدم محدد
  Future<List<ActivityLog>> getUserActivityLogs(int userId) async {
    try {
      final response = await _apiService.get('/ActivityLogs/user/$userId');
      return _apiService.handleListResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات المستخدم $userId: $e');
      rethrow;
    }
  }

  /// الحصول على سجلات أنشطة المستخدم الحالي
  Future<List<ActivityLog>> getCurrentUserActivityLogs() async {
    try {
      final response = await _apiService.get('/ActivityLogs/my-activities');
      return _apiService.handleListResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات المستخدم الحالي: $e');
      rethrow;
    }
  }

  /// الحصول على سجلات أنشطة مهمة محددة
  Future<List<ActivityLog>> getTaskActivityLogs(int taskId) async {
    try {
      final response = await _apiService.get('/ActivityLogs/task/$taskId');
      return _apiService.handleListResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات المهمة $taskId: $e');
      rethrow;
    }
  }

  /// الحصول على سجلات الأنشطة بحسب النوع
  Future<List<ActivityLog>> getActivityLogsByType(String activityType) async {
    try {
      final response = await _apiService.get('/ActivityLogs/type/$activityType');
      return _apiService.handleListResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات النوع $activityType: $e');
      return [];
    }
  }

  /// الحصول على سجلات الأنشطة الحديثة
  Future<List<ActivityLog>> getRecentActivityLogs({int limit = 50}) async {
    try {
      final response = await _apiService.get('/ActivityLogs/recent?limit=$limit');
      return _apiService.handleListResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على السجلات الحديثة: $e');
      return [];
    }
  }

  /// الحصول على سجلات الأنشطة بحسب التاريخ
  Future<List<ActivityLog>> getActivityLogsByDate(DateTime date) async {
    try {
      final dateStr = date.toIso8601String().split('T')[0];
      final response = await _apiService.get('/ActivityLogs/date/$dateStr');
      return _apiService.handleListResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات التاريخ: $e');
      return [];
    }
  }

  /// الحصول على سجلات الأنشطة لفترة زمنية
  Future<List<ActivityLog>> getActivityLogsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final startStr = startDate.toIso8601String().split('T')[0];
      final endStr = endDate.toIso8601String().split('T')[0];
      final response = await _apiService.get('/ActivityLogs/range?start=$startStr&end=$endStr');
      return _apiService.handleListResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات الفترة الزمنية: $e');
      return [];
    }
  }

  /// إنشاء سجل نشاط جديد
  Future<ActivityLog> createActivityLog(ActivityLog activityLog) async {
    try {
      final response = await _apiService.post(
        '/ActivityLogs',
        body: activityLog.toJson(),
      );
      return _apiService.handleResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء سجل النشاط: $e');
      rethrow;
    }
  }

  /// تسجيل نشاط بسيط
  Future<ActivityLog> logActivity({
    required String action,
    required String entityType,
    required int entityId,
    String? description,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await _apiService.post(
        '/ActivityLogs/log',
        body: {
          'action': action,
          'entityType': entityType,
          'entityId': entityId,
          'description': description,
          'metadata': metadata,
        },
      );
      return _apiService.handleResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل النشاط: $e');
      rethrow;
    }
  }

  /// تحديث سجل نشاط
  Future<ActivityLog> updateActivityLog(int id, ActivityLog activityLog) async {
    try {
      final response = await _apiService.put(
        '/ActivityLogs/$id',
        body: activityLog.toJson(),
      );
      return _apiService.handleResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث سجل النشاط $id: $e');
      rethrow;
    }
  }

  /// حذف سجل نشاط
  Future<bool> deleteActivityLog(int id) async {
    try {
      final response = await _apiService.delete('/ActivityLogs/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف سجل النشاط $id: $e');
      return false;
    }
  }

  /// البحث في سجلات الأنشطة
  Future<List<ActivityLog>> searchActivityLogs(String query) async {
    try {
      final response = await _apiService.get('/ActivityLogs/search?q=$query');
      return _apiService.handleListResponse<ActivityLog>(
        response,
        (json) => ActivityLog.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في سجلات الأنشطة: $e');
      return [];
    }
  }

  /// الحصول على أنواع الأنشطة المتاحة
  Future<List<String>> getAvailableActivityTypes() async {
    try {
      final response = await _apiService.get('/ActivityLogs/activity-types');
      return _apiService.handleListResponse<String>(
        response,
        (json) => json as String,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أنواع الأنشطة: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات الأنشطة
  Future<Map<String, dynamic>> getActivityStatistics() async {
    try {
      final response = await _apiService.get('/ActivityLogs/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات الأنشطة: $e');
      return {};
    }
  }

  /// الحصول على إحصائيات أنشطة مستخدم محدد
  Future<Map<String, dynamic>> getUserActivityStatistics(int userId) async {
    try {
      final response = await _apiService.get('/ActivityLogs/user/$userId/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات المستخدم: $e');
      return {};
    }
  }

  /// الحصول على تقرير الأنشطة اليومي
  Future<Map<String, dynamic>> getDailyActivityReport(DateTime date) async {
    try {
      final dateStr = date.toIso8601String().split('T')[0];
      final response = await _apiService.get('/ActivityLogs/reports/daily?date=$dateStr');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على التقرير اليومي: $e');
      return {};
    }
  }

  /// الحصول على تقرير الأنشطة الأسبوعي
  Future<Map<String, dynamic>> getWeeklyActivityReport(DateTime weekStart) async {
    try {
      final dateStr = weekStart.toIso8601String().split('T')[0];
      final response = await _apiService.get('/ActivityLogs/reports/weekly?weekStart=$dateStr');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على التقرير الأسبوعي: $e');
      return {};
    }
  }

  /// تصدير سجلات الأنشطة
  Future<Map<String, dynamic>> exportActivityLogs(
    DateTime startDate,
    DateTime endDate,
    String format,
  ) async {
    try {
      final startStr = startDate.toIso8601String().split('T')[0];
      final endStr = endDate.toIso8601String().split('T')[0];
      final response = await _apiService.get(
        '/ActivityLogs/export?start=$startStr&end=$endStr&format=$format',
      );
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في تصدير سجلات الأنشطة: $e');
      return {};
    }
  }

  /// حذف سجلات الأنشطة القديمة
  Future<bool> cleanupOldActivityLogs(int daysToKeep) async {
    try {
      final response = await _apiService.delete('/ActivityLogs/cleanup?daysToKeep=$daysToKeep');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تنظيف السجلات القديمة: $e');
      return false;
    }
  }

  /// الحصول على سجلات الأنشطة المجمعة بحسب النوع
  Future<Map<String, dynamic>> getActivityLogsSummaryByType() async {
    try {
      final response = await _apiService.get('/ActivityLogs/summary/by-type');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على ملخص الأنشطة بحسب النوع: $e');
      return {};
    }
  }

  /// الحصول على سجلات الأنشطة المجمعة بحسب المستخدم
  Future<Map<String, dynamic>> getActivityLogsSummaryByUser() async {
    try {
      final response = await _apiService.get('/ActivityLogs/summary/by-user');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على ملخص الأنشطة بحسب المستخدم: $e');
      return {};
    }
  }

  /// تسجيل نشاط تسجيل الدخول
  Future<ActivityLog> logLoginActivity() async {
    return await logActivity(
      action: 'login',
      entityType: 'user',
      entityId: 0, // سيتم تحديده من الخادم
      description: 'تسجيل دخول المستخدم',
    );
  }

  /// تسجيل نشاط تسجيل الخروج
  Future<ActivityLog> logLogoutActivity() async {
    return await logActivity(
      action: 'logout',
      entityType: 'user',
      entityId: 0, // سيتم تحديده من الخادم
      description: 'تسجيل خروج المستخدم',
    );
  }
}
