import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/system_models.dart';
import '../services/api/system_logs_api_service.dart';

/// متحكم سجلات النظام
class SystemLogsController extends GetxController {
  final SystemLogsApiService _apiService = SystemLogsApiService();

  // قوائم السجلات
  final RxList<SystemLog> _allLogs = <SystemLog>[].obs;
  final RxList<SystemLog> _filteredLogs = <SystemLog>[].obs;
  final RxList<SystemLog> _errorLogs = <SystemLog>[].obs;
  final RxList<SystemLog> _warningLogs = <SystemLog>[].obs;

  // السجل الحالي
  final Rx<SystemLog?> _currentLog = Rx<SystemLog?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<String?> _levelFilter = Rx<String?>(null);
  final Rx<String?> _categoryFilter = Rx<String?>(null);
  final Rx<DateTime?> _dateFromFilter = Rx<DateTime?>(null);
  final Rx<DateTime?> _dateToFilter = Rx<DateTime?>(null);
  final Rx<int?> _userFilter = Rx<int?>(null);

  // إحصائيات
  final RxInt _totalLogs = 0.obs;
  final RxInt _errorCount = 0.obs;
  final RxInt _warningCount = 0.obs;
  final RxInt _infoCount = 0.obs;

  // Getters
  List<SystemLog> get allLogs => _allLogs;
  List<SystemLog> get filteredLogs => _filteredLogs;
  List<SystemLog> get errorLogs => _errorLogs;
  List<SystemLog> get warningLogs => _warningLogs;
  SystemLog? get currentLog => _currentLog.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  String? get levelFilter => _levelFilter.value;
  String? get categoryFilter => _categoryFilter.value;
  DateTime? get dateFromFilter => _dateFromFilter.value;
  DateTime? get dateToFilter => _dateToFilter.value;
  int? get userFilter => _userFilter.value;
  int get totalLogs => _totalLogs.value;
  int get errorCount => _errorCount.value;
  int get warningCount => _warningCount.value;
  int get infoCount => _infoCount.value;

  @override
  void onInit() {
    super.onInit();
    loadAllLogs();
    loadErrorLogs();
    loadWarningLogs();
    loadStatistics();
  }

  /// تحميل جميع سجلات النظام
  Future<void> loadAllLogs() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final logs = await _apiService.getAllLogs();
      _allLogs.assignAll(logs);
      _applyFilters();
      debugPrint('تم تحميل ${logs.length} سجل نظام');
    } catch (e) {
      _error.value = 'خطأ في تحميل سجلات النظام: $e';
      debugPrint('خطأ في تحميل سجلات النظام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل سجلات الأخطاء
  Future<void> loadErrorLogs() async {
    try {
      final logs = await _apiService.getLogsByLevel('error');
      _errorLogs.assignAll(logs);
      debugPrint('تم تحميل ${logs.length} سجل خطأ');
    } catch (e) {
      debugPrint('خطأ في تحميل سجلات الأخطاء: $e');
    }
  }

  /// تحميل سجلات التحذيرات
  Future<void> loadWarningLogs() async {
    try {
      final logs = await _apiService.getLogsByLevel('warning');
      _warningLogs.assignAll(logs);
      debugPrint('تم تحميل ${logs.length} سجل تحذير');
    } catch (e) {
      debugPrint('خطأ في تحميل سجلات التحذيرات: $e');
    }
  }

  /// تحميل الإحصائيات
  Future<void> loadStatistics() async {
    try {
      final stats = await _apiService.getStatistics();
      _totalLogs.value = stats['total'] ?? 0;
      _errorCount.value = stats['error'] ?? 0;
      _warningCount.value = stats['warning'] ?? 0;
      _infoCount.value = stats['info'] ?? 0;
      debugPrint('تم تحميل إحصائيات سجلات النظام');
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات سجلات النظام: $e');
    }
  }

  /// الحصول على سجل نظام بالمعرف
  Future<void> getLogById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final log = await _apiService.getLogById(id);
      _currentLog.value = log;
      debugPrint('تم تحميل سجل النظام');
    } catch (e) {
      _error.value = 'خطأ في تحميل سجل النظام: $e';
      debugPrint('خطأ في تحميل سجل النظام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء سجل نظام جديد
  Future<bool> createLog(SystemLog log) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newLog = await _apiService.createLog(log);
      _allLogs.insert(0, newLog);
      _applyFilters();
      await loadStatistics();
      debugPrint('تم إنشاء سجل نظام جديد');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء سجل النظام: $e';
      debugPrint('خطأ في إنشاء سجل النظام: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف سجل نظام
  Future<bool> deleteLog(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteLog(id);
      _allLogs.removeWhere((l) => l.id == id);
      _applyFilters();
      await loadStatistics();
      debugPrint('تم حذف سجل النظام');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف سجل النظام: $e';
      debugPrint('خطأ في حذف سجل النظام: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على السجلات حسب المستوى
  Future<void> getLogsByLevel(String level) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final logs = await _apiService.getLogsByLevel(level);
      _allLogs.assignAll(logs);
      _applyFilters();
      debugPrint('تم تحميل ${logs.length} سجل من مستوى $level');
    } catch (e) {
      _error.value = 'خطأ في تحميل سجلات المستوى: $e';
      debugPrint('خطأ في تحميل سجلات المستوى: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على السجلات حسب الفئة
  Future<void> getLogsByCategory(String category) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final logs = await _apiService.getLogsByCategory(category);
      _allLogs.assignAll(logs);
      _applyFilters();
      debugPrint('تم تحميل ${logs.length} سجل من فئة $category');
    } catch (e) {
      _error.value = 'خطأ في تحميل سجلات الفئة: $e';
      debugPrint('خطأ في تحميل سجلات الفئة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على السجلات حسب التاريخ
  Future<void> getLogsByDateRange(DateTime from, DateTime to) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final logs = await _apiService.getLogsByDateRange(from, to);
      _allLogs.assignAll(logs);
      _applyFilters();
      debugPrint('تم تحميل ${logs.length} سجل للفترة المحددة');
    } catch (e) {
      _error.value = 'خطأ في تحميل سجلات الفترة: $e';
      debugPrint('خطأ في تحميل سجلات الفترة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// مسح السجلات القديمة
  Future<bool> clearOldLogs(int daysOld) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final deletedCount = await _apiService.clearOldLogs(daysOld);
      await loadAllLogs();
      await loadStatistics();
      debugPrint('تم مسح $deletedCount سجل قديم');
      return true;
    } catch (e) {
      _error.value = 'خطأ في مسح السجلات القديمة: $e';
      debugPrint('خطأ في مسح السجلات القديمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تصدير السجلات
  Future<String?> exportLogs(String format, DateTime? from, DateTime? to) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final filePath = await _apiService.exportLogs(format, from, to);
      debugPrint('تم تصدير السجلات: $filePath');
      return filePath;
    } catch (e) {
      _error.value = 'خطأ في تصدير السجلات: $e';
      debugPrint('خطأ في تصدير السجلات: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تسجيل حدث جديد
  Future<bool> logEvent(String level, String category, String message, Map<String, dynamic>? metadata) async {
    try {
      await _apiService.logEvent(level, category, message, metadata);
      debugPrint('تم تسجيل حدث جديد: $message');
      return true;
    } catch (e) {
      debugPrint('خطأ في تسجيل الحدث: $e');
      return false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allLogs.where((log) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!log.message.toLowerCase().contains(query) &&
            !log.category.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح المستوى
      if (_levelFilter.value != null && log.level != _levelFilter.value) {
        return false;
      }

      // مرشح الفئة
      if (_categoryFilter.value != null && log.category != _categoryFilter.value) {
        return false;
      }

      // مرشح التاريخ من
      if (_dateFromFilter.value != null) {
        final logDate = DateTime.fromMillisecondsSinceEpoch(log.timestamp * 1000);
        if (logDate.isBefore(_dateFromFilter.value!)) {
          return false;
        }
      }

      // مرشح التاريخ إلى
      if (_dateToFilter.value != null) {
        final logDate = DateTime.fromMillisecondsSinceEpoch(log.timestamp * 1000);
        if (logDate.isAfter(_dateToFilter.value!)) {
          return false;
        }
      }

      // مرشح المستخدم
      if (_userFilter.value != null && log.userId != _userFilter.value) {
        return false;
      }

      return true;
    }).toList();

    _filteredLogs.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح المستوى
  void setLevelFilter(String? level) {
    _levelFilter.value = level;
    _applyFilters();
  }

  /// تعيين مرشح الفئة
  void setCategoryFilter(String? category) {
    _categoryFilter.value = category;
    _applyFilters();
  }

  /// تعيين مرشح التاريخ من
  void setDateFromFilter(DateTime? date) {
    _dateFromFilter.value = date;
    _applyFilters();
  }

  /// تعيين مرشح التاريخ إلى
  void setDateToFilter(DateTime? date) {
    _dateToFilter.value = date;
    _applyFilters();
  }

  /// تعيين مرشح المستخدم
  void setUserFilter(int? userId) {
    _userFilter.value = userId;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _levelFilter.value = null;
    _categoryFilter.value = null;
    _dateFromFilter.value = null;
    _dateToFilter.value = null;
    _userFilter.value = null;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await Future.wait([
      loadAllLogs(),
      loadErrorLogs(),
      loadWarningLogs(),
      loadStatistics(),
    ]);
  }
}
