import 'package:flutter/foundation.dart';
import '../../models/backup_model.dart';
import 'api_service.dart';

/// خدمة API للنسخ الاحتياطي - متطابقة مع ASP.NET Core API
class BackupApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع النسخ الاحتياطية
  Future<List<Backup>> getAllBackups() async {
    try {
      final response = await _apiService.get('/Backup');
      return _apiService.handleListResponse<Backup>(
        response,
        (json) => Backup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على النسخ الاحتياطية: $e');
      rethrow;
    }
  }

  /// الحصول على نسخة احتياطية بواسطة المعرف
  Future<Backup?> getBackupById(int id) async {
    try {
      final response = await _apiService.get('/Backup/$id');
      return _apiService.handleResponse<Backup>(
        response,
        (json) => Backup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على النسخة الاحتياطية $id: $e');
      return null;
    }
  }

  /// الحصول على النسخ الاحتياطية الحديثة
  Future<List<Backup>> getRecentBackups({int limit = 10}) async {
    try {
      final response = await _apiService.get('/Backup/recent?limit=$limit');
      return _apiService.handleListResponse<Backup>(
        response,
        (json) => Backup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على النسخ الاحتياطية الحديثة: $e');
      return [];
    }
  }

  /// الحصول على النسخ الاحتياطية الناجحة
  Future<List<Backup>> getSuccessfulBackups() async {
    try {
      final response = await _apiService.get('/Backup/successful');
      return _apiService.handleListResponse<Backup>(
        response,
        (json) => Backup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على النسخ الاحتياطية الناجحة: $e');
      return [];
    }
  }

  /// الحصول على النسخ الاحتياطية الفاشلة
  Future<List<Backup>> getFailedBackups() async {
    try {
      final response = await _apiService.get('/Backup/failed');
      return _apiService.handleListResponse<Backup>(
        response,
        (json) => Backup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على النسخ الاحتياطية الفاشلة: $e');
      return [];
    }
  }

  /// إنشاء نسخة احتياطية جديدة
  Future<Backup> createBackup({
    required String name,
    String? description,
    List<String>? includeTables,
    List<String>? excludeTables,
    bool includeData = true,
    bool includeSchema = true,
  }) async {
    try {
      final response = await _apiService.post(
        '/Backup/create',
        body: {
          'name': name,
          'description': description,
          'includeTables': includeTables,
          'excludeTables': excludeTables,
          'includeData': includeData,
          'includeSchema': includeSchema,
        },
      );
      return _apiService.handleResponse<Backup>(
        response,
        (json) => Backup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء النسخة الاحتياطية: $e');
      rethrow;
    }
  }

  /// إنشاء نسخة احتياطية كاملة
  Future<Backup> createFullBackup({String? description}) async {
    try {
      final response = await _apiService.post(
        '/Backup/create-full',
        body: {
          'description': description ?? 'نسخة احتياطية كاملة',
        },
      );
      return _apiService.handleResponse<Backup>(
        response,
        (json) => Backup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء النسخة الاحتياطية الكاملة: $e');
      rethrow;
    }
  }

  /// إنشاء نسخة احتياطية تزايدية
  Future<Backup> createIncrementalBackup({String? description}) async {
    try {
      final response = await _apiService.post(
        '/Backup/create-incremental',
        body: {
          'description': description ?? 'نسخة احتياطية تزايدية',
        },
      );
      return _apiService.handleResponse<Backup>(
        response,
        (json) => Backup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء النسخة الاحتياطية التزايدية: $e');
      rethrow;
    }
  }

  /// استعادة نسخة احتياطية
  Future<bool> restoreBackup(int backupId, {bool overwriteExisting = false}) async {
    try {
      final response = await _apiService.post(
        '/Backup/$backupId/restore',
        body: {
          'overwriteExisting': overwriteExisting,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في استعادة النسخة الاحتياطية: $e');
      return false;
    }
  }

  /// حذف نسخة احتياطية
  Future<bool> deleteBackup(int id) async {
    try {
      final response = await _apiService.delete('/Backup/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف النسخة الاحتياطية $id: $e');
      return false;
    }
  }

  /// تنزيل نسخة احتياطية
  Future<Map<String, dynamic>> downloadBackup(int id) async {
    try {
      final response = await _apiService.get('/Backup/$id/download');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في تنزيل النسخة الاحتياطية: $e');
      return {};
    }
  }

  /// الحصول على حالة النسخة الاحتياطية
  Future<Map<String, dynamic>> getBackupStatus(int id) async {
    try {
      final response = await _apiService.get('/Backup/$id/status');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على حالة النسخة الاحتياطية: $e');
      return {};
    }
  }

  /// إلغاء نسخة احتياطية جارية
  Future<bool> cancelBackup(int id) async {
    try {
      final response = await _apiService.post(
        '/Backup/$id/cancel',
        body: {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء النسخة الاحتياطية: $e');
      return false;
    }
  }

  /// جدولة نسخة احتياطية تلقائية
  Future<bool> scheduleBackup({
    required String name,
    required String cronExpression,
    String? description,
    String backupType = 'full',
  }) async {
    try {
      final response = await _apiService.post(
        '/Backup/schedule',
        body: {
          'name': name,
          'cronExpression': cronExpression,
          'description': description,
          'backupType': backupType,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في جدولة النسخة الاحتياطية: $e');
      return false;
    }
  }

  /// إلغاء جدولة نسخة احتياطية
  Future<bool> unscheduleBackup(int scheduleId) async {
    try {
      final response = await _apiService.delete('/Backup/schedule/$scheduleId');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء جدولة النسخة الاحتياطية: $e');
      return false;
    }
  }

  /// الحصول على جداول النسخ الاحتياطية
  Future<List<Map<String, dynamic>>> getBackupSchedules() async {
    try {
      final response = await _apiService.get('/Backup/schedules');
      return _apiService.handleListResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على جداول النسخ الاحتياطية: $e');
      return [];
    }
  }

  /// التحقق من صحة النسخة الاحتياطية
  Future<Map<String, dynamic>> validateBackup(int id) async {
    try {
      final response = await _apiService.post(
        '/Backup/$id/validate',
        body: {},
      );
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في التحقق من صحة النسخة الاحتياطية: $e');
      return {'isValid': false, 'error': e.toString()};
    }
  }

  /// الحصول على إحصائيات النسخ الاحتياطية
  Future<Map<String, dynamic>> getBackupStatistics() async {
    try {
      final response = await _apiService.get('/Backup/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات النسخ الاحتياطية: $e');
      return {};
    }
  }

  /// الحصول على مساحة التخزين المستخدمة
  Future<Map<String, dynamic>> getStorageUsage() async {
    try {
      final response = await _apiService.get('/Backup/storage-usage');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مساحة التخزين: $e');
      return {};
    }
  }

  /// تنظيف النسخ الاحتياطية القديمة
  Future<bool> cleanupOldBackups({int daysToKeep = 30}) async {
    try {
      final response = await _apiService.delete('/Backup/cleanup?daysToKeep=$daysToKeep');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تنظيف النسخ الاحتياطية القديمة: $e');
      return false;
    }
  }

  /// اختبار اتصال قاعدة البيانات
  Future<Map<String, dynamic>> testDatabaseConnection() async {
    try {
      final response = await _apiService.get('/Backup/test-connection');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في اختبار اتصال قاعدة البيانات: $e');
      return {'isConnected': false, 'error': e.toString()};
    }
  }

  /// الحصول على معلومات قاعدة البيانات
  Future<Map<String, dynamic>> getDatabaseInfo() async {
    try {
      final response = await _apiService.get('/Backup/database-info');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على معلومات قاعدة البيانات: $e');
      return {};
    }
  }

  /// الحصول على قائمة الجداول المتاحة
  Future<List<String>> getAvailableTables() async {
    try {
      final response = await _apiService.get('/Backup/available-tables');
      return _apiService.handleListResponse<String>(
        response,
        (json) => json as String,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على قائمة الجداول: $e');
      return [];
    }
  }

  /// تصدير إعدادات النسخ الاحتياطية
  Future<Map<String, dynamic>> exportBackupSettings() async {
    try {
      final response = await _apiService.get('/Backup/export-settings');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في تصدير إعدادات النسخ الاحتياطية: $e');
      return {};
    }
  }

  /// استيراد إعدادات النسخ الاحتياطية
  Future<bool> importBackupSettings(Map<String, dynamic> settings) async {
    try {
      final response = await _apiService.post(
        '/Backup/import-settings',
        body: settings,
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في استيراد إعدادات النسخ الاحتياطية: $e');
      return false;
    }
  }
}
