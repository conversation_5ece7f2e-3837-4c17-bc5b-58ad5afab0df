import 'package:flutter/foundation.dart';
import '../../models/attachment_models.dart';
import 'api_service.dart';

/// خدمة API للمرفقات - متطابقة مع ASP.NET Core API
class AttachmentsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع المرفقات
  Future<List<Attachment>> getAllAttachments() async {
    try {
      final response = await _apiService.get('/Attachments');
      return _apiService.handleListResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المرفقات: $e');
      rethrow;
    }
  }

  /// الحصول على مرفق بواسطة المعرف
  Future<Attachment?> getAttachmentById(int id) async {
    try {
      final response = await _apiService.get('/Attachments/$id');
      return _apiService.handleResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المرفق: $e');
      return null;
    }
  }

  /// إنشاء مرفق جديد
  Future<Attachment?> createAttachment(Attachment attachment) async {
    try {
      final response = await _apiService.post(
        '/Attachments',
        body: attachment.toJson(),
      );
      return _apiService.handleResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء المرفق: $e');
      rethrow;
    }
  }

  /// تحديث مرفق
  Future<Attachment?> updateAttachment(Attachment attachment) async {
    try {
      final response = await _apiService.put(
        '/Attachments/${attachment.id}',
        body: attachment.toJson(),
      );
      return _apiService.handleResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث المرفق: $e');
      rethrow;
    }
  }

  /// حذف مرفق
  Future<bool> deleteAttachment(int id) async {
    try {
      final response = await _apiService.delete('/Attachments/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف المرفق: $e');
      return false;
    }
  }

  /// الحصول على مرفقات مهمة معينة
  Future<List<Attachment>> getAttachmentsByTask(int taskId) async {
    try {
      final response = await _apiService.get('/Attachments/task/$taskId');
      return _apiService.handleListResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مرفقات المهمة: $e');
      rethrow;
    }
  }

  /// الحصول على مرفقات مشروع معين
  Future<List<Attachment>> getAttachmentsByProject(int projectId) async {
    try {
      final response = await _apiService.get('/Attachments/project/$projectId');
      return _apiService.handleListResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مرفقات المشروع: $e');
      rethrow;
    }
  }

  /// الحصول على مرفقات مستخدم معين
  Future<List<Attachment>> getAttachmentsByUser(int userId) async {
    try {
      final response = await _apiService.get('/Attachments/user/$userId');
      return _apiService.handleListResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مرفقات المستخدم: $e');
      rethrow;
    }
  }

  /// البحث في المرفقات
  Future<List<Attachment>> searchAttachments(String query) async {
    try {
      final response = await _apiService.get('/Attachments/search?searchTerm=$query');
      return _apiService.handleListResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن المرفقات: $e');
      rethrow;
    }
  }

  /// الحصول على المرفقات بنوع ملف معين
  Future<List<Attachment>> getAttachmentsByFileType(String fileType) async {
    try {
      final response = await _apiService.get('/Attachments/file-type/$fileType');
      return _apiService.handleListResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مرفقات نوع الملف: $e');
      rethrow;
    }
  }

  /// الحصول على المرفقات في فترة زمنية
  Future<List<Attachment>> getAttachmentsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final startTimestamp = startDate.millisecondsSinceEpoch ~/ 1000;
      final endTimestamp = endDate.millisecondsSinceEpoch ~/ 1000;
      
      final response = await _apiService.get(
        '/Attachments/date-range?startDate=$startTimestamp&endDate=$endTimestamp'
      );
      return _apiService.handleListResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مرفقات الفترة الزمنية: $e');
      rethrow;
    }
  }

  /// رفع ملف مرفق
  Future<Attachment?> uploadFile(
    String fileName,
    String filePath,
    int? taskId,
    int? projectId,
  ) async {
    try {
      // TODO: تنفيذ رفع الملف باستخدام multipart/form-data
      final response = await _apiService.post(
        '/Attachments/upload',
        body: {
          'fileName': fileName,
          'filePath': filePath,
          'taskId': taskId,
          'projectId': projectId,
        },
      );
      return _apiService.handleResponse<Attachment>(
        response,
        (json) => Attachment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في رفع الملف: $e');
      rethrow;
    }
  }

  /// تحميل ملف مرفق
  Future<String?> downloadFile(int attachmentId) async {
    try {
      final response = await _apiService.get('/Attachments/$attachmentId/download');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تحميل الملف: $e');
      return null;
    }
  }

  /// الحصول على رابط تحميل مؤقت
  Future<String?> getTemporaryDownloadLink(int attachmentId) async {
    try {
      final response = await _apiService.get('/Attachments/$attachmentId/temp-link');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على رابط التحميل المؤقت: $e');
      return null;
    }
  }

  /// الحصول على معاينة الملف
  Future<String?> getFilePreview(int attachmentId) async {
    try {
      final response = await _apiService.get('/Attachments/$attachmentId/preview');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على معاينة الملف: $e');
      return null;
    }
  }

  /// الحصول على صورة مصغرة للملف
  Future<String?> getFileThumbnail(int attachmentId) async {
    try {
      final response = await _apiService.get('/Attachments/$attachmentId/thumbnail');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على الصورة المصغرة: $e');
      return null;
    }
  }

  /// الحصول على إحصائيات المرفقات
  Future<Map<String, dynamic>?> getAttachmentStatistics() async {
    try {
      final response = await _apiService.get('/Attachments/statistics');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return Map<String, dynamic>.from(response.body as Map);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات المرفقات: $e');
      return null;
    }
  }

  /// الحصول على أحجام الملفات
  Future<Map<String, dynamic>?> getFileSizes() async {
    try {
      final response = await _apiService.get('/Attachments/file-sizes');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return Map<String, dynamic>.from(response.body as Map);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على أحجام الملفات: $e');
      return null;
    }
  }

  /// حذف مرفقات متعددة
  Future<int> deleteMultipleAttachments(List<int> attachmentIds) async {
    try {
      final response = await _apiService.delete(
        '/Attachments/bulk-delete',
        body: {'attachmentIds': attachmentIds},
      );
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return int.tryParse(response.body.toString()) ?? 0;
      }
      return 0;
    } catch (e) {
      debugPrint('خطأ في حذف المرفقات المتعددة: $e');
      return 0;
    }
  }

  /// نقل مرفقات إلى مهمة أخرى
  Future<bool> moveAttachmentsToTask(List<int> attachmentIds, int newTaskId) async {
    try {
      final response = await _apiService.put(
        '/Attachments/move-to-task',
        body: {
          'attachmentIds': attachmentIds,
          'newTaskId': newTaskId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في نقل المرفقات: $e');
      return false;
    }
  }

  /// نسخ مرفقات إلى مهمة أخرى
  Future<bool> copyAttachmentsToTask(List<int> attachmentIds, int targetTaskId) async {
    try {
      final response = await _apiService.post(
        '/Attachments/copy-to-task',
        body: {
          'attachmentIds': attachmentIds,
          'targetTaskId': targetTaskId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في نسخ المرفقات: $e');
      return false;
    }
  }

  /// ضغط مرفقات متعددة
  Future<String?> compressAttachments(List<int> attachmentIds, String archiveName) async {
    try {
      final response = await _apiService.post(
        '/Attachments/compress',
        body: {
          'attachmentIds': attachmentIds,
          'archiveName': archiveName,
        },
      );
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في ضغط المرفقات: $e');
      return null;
    }
  }
}
