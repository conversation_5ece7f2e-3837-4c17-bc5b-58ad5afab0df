import 'package:flutter/foundation.dart';
import '../../models/report_model.dart';
import 'api_service.dart';

/// خدمة API للتقارير - متطابقة مع ASP.NET Core API
class ReportsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع التقارير
  Future<List<Report>> getAllReports() async {
    try {
      final response = await _apiService.get('/Reports');
      return _apiService.handleListResponse<Report>(
        response,
        (json) => Report.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على التقارير: $e');
      rethrow;
    }
  }

  /// الحصول على تقرير بواسطة المعرف
  Future<Report?> getReportById(int id) async {
    try {
      final response = await _apiService.get('/Reports/$id');
      return _apiService.handleResponse<Report>(
        response,
        (json) => Report.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على التقرير $id: $e');
      return null;
    }
  }

  /// الحصول على تقارير مستخدم محدد
  Future<List<Report>> getUserReports(int userId) async {
    try {
      final response = await _apiService.get('/Reports/user/$userId');
      return _apiService.handleListResponse<Report>(
        response,
        (json) => Report.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على تقارير المستخدم $userId: $e');
      rethrow;
    }
  }

  /// الحصول على التقارير العامة
  Future<List<Report>> getPublicReports() async {
    try {
      final response = await _apiService.get('/Reports/public');
      return _apiService.handleListResponse<Report>(
        response,
        (json) => Report.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على التقارير العامة: $e');
      rethrow;
    }
  }

  /// الحصول على التقارير بحسب النوع
  Future<List<Report>> getReportsByType(String reportType) async {
    try {
      final response = await _apiService.get('/Reports/type/$reportType');
      return _apiService.handleListResponse<Report>(
        response,
        (json) => Report.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على التقارير بحسب النوع: $e');
      return [];
    }
  }

  /// الحصول على التقارير المفضلة للمستخدم الحالي
  Future<List<Report>> getFavoriteReports() async {
    try {
      final response = await _apiService.get('/Reports/favorites');
      return _apiService.handleListResponse<Report>(
        response,
        (json) => Report.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على التقارير المفضلة: $e');
      return [];
    }
  }

  /// إنشاء تقرير جديد
  Future<Report> createReport(Report report) async {
    try {
      final response = await _apiService.post(
        '/Reports',
        body: report.toJson(),
      );
      return _apiService.handleResponse<Report>(
        response,
        (json) => Report.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء التقرير: $e');
      rethrow;
    }
  }

  /// تحديث تقرير
  Future<Report> updateReport(int id, Report report) async {
    try {
      final response = await _apiService.put(
        '/Reports/$id',
        body: report.toJson(),
      );
      return _apiService.handleResponse<Report>(
        response,
        (json) => Report.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث التقرير $id: $e');
      rethrow;
    }
  }

  /// حذف تقرير
  Future<bool> deleteReport(int id) async {
    try {
      final response = await _apiService.delete('/Reports/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف التقرير $id: $e');
      return false;
    }
  }

  /// تشغيل تقرير وإنتاج البيانات
  Future<Map<String, dynamic>> runReport(int id, Map<String, dynamic>? parameters) async {
    try {
      final response = await _apiService.post(
        '/Reports/$id/run',
        body: parameters ?? {},
      );
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في تشغيل التقرير: $e');
      rethrow;
    }
  }

  /// تصدير تقرير بصيغة محددة
  Future<Map<String, dynamic>> exportReport(
    int id,
    String format,
    Map<String, dynamic>? parameters,
  ) async {
    try {
      final response = await _apiService.post(
        '/Reports/$id/export',
        body: {
          'format': format,
          'parameters': parameters ?? {},
        },
      );
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير: $e');
      rethrow;
    }
  }

  /// جدولة تقرير للتشغيل التلقائي
  Future<bool> scheduleReport(int id, Map<String, dynamic> scheduleConfig) async {
    try {
      final response = await _apiService.post(
        '/Reports/$id/schedule',
        body: scheduleConfig,
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في جدولة التقرير: $e');
      return false;
    }
  }

  /// إلغاء جدولة تقرير
  Future<bool> unscheduleReport(int id) async {
    try {
      final response = await _apiService.delete('/Reports/$id/schedule');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء جدولة التقرير: $e');
      return false;
    }
  }

  /// إضافة تقرير للمفضلة
  Future<bool> addToFavorites(int id) async {
    try {
      final response = await _apiService.post(
        '/Reports/$id/favorite',
        body: {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إضافة التقرير للمفضلة: $e');
      return false;
    }
  }

  /// إزالة تقرير من المفضلة
  Future<bool> removeFromFavorites(int id) async {
    try {
      final response = await _apiService.delete('/Reports/$id/favorite');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إزالة التقرير من المفضلة: $e');
      return false;
    }
  }

  /// مشاركة تقرير مع مستخدمين آخرين
  Future<bool> shareReport(int id, List<int> userIds, String permission) async {
    try {
      final response = await _apiService.post(
        '/Reports/$id/share',
        body: {
          'userIds': userIds,
          'permission': permission,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في مشاركة التقرير: $e');
      return false;
    }
  }

  /// إلغاء مشاركة تقرير
  Future<bool> unshareReport(int id, int userId) async {
    try {
      final response = await _apiService.delete('/Reports/$id/share/$userId');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء مشاركة التقرير: $e');
      return false;
    }
  }

  /// البحث في التقارير
  Future<List<Report>> searchReports(String query) async {
    try {
      final response = await _apiService.get('/Reports/search?q=$query');
      return _apiService.handleListResponse<Report>(
        response,
        (json) => Report.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في التقارير: $e');
      return [];
    }
  }

  /// الحصول على قوالب التقارير
  Future<List<Map<String, dynamic>>> getReportTemplates() async {
    try {
      final response = await _apiService.get('/Reports/templates');
      return _apiService.handleListResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على قوالب التقارير: $e');
      return [];
    }
  }

  /// إنشاء تقرير من قالب
  Future<Report> createReportFromTemplate(int templateId, Map<String, dynamic> config) async {
    try {
      final response = await _apiService.post(
        '/Reports/from-template/$templateId',
        body: config,
      );
      return _apiService.handleResponse<Report>(
        response,
        (json) => Report.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء تقرير من قالب: $e');
      rethrow;
    }
  }

  /// نسخ تقرير
  Future<Report> duplicateReport(int id, String newName) async {
    try {
      final response = await _apiService.post(
        '/Reports/$id/duplicate',
        body: {
          'newName': newName,
        },
      );
      return _apiService.handleResponse<Report>(
        response,
        (json) => Report.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في نسخ التقرير: $e');
      rethrow;
    }
  }

  /// الحصول على سجل تشغيل التقارير
  Future<List<Map<String, dynamic>>> getReportExecutionHistory(int id) async {
    try {
      final response = await _apiService.get('/Reports/$id/execution-history');
      return _apiService.handleListResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجل تشغيل التقرير: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات التقارير
  Future<Map<String, dynamic>> getReportsStatistics() async {
    try {
      final response = await _apiService.get('/Reports/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات التقارير: $e');
      return {};
    }
  }

  /// التحقق من صحة استعلام التقرير
  Future<Map<String, dynamic>> validateReportQuery(String query) async {
    try {
      final response = await _apiService.post(
        '/Reports/validate-query',
        body: {
          'query': query,
        },
      );
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في التحقق من صحة الاستعلام: $e');
      return {'isValid': false, 'error': e.toString()};
    }
  }

  /// الحصول على مصادر البيانات المتاحة
  Future<List<Map<String, dynamic>>> getAvailableDataSources() async {
    try {
      final response = await _apiService.get('/Reports/data-sources');
      return _apiService.handleListResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مصادر البيانات: $e');
      return [];
    }
  }

  /// الحصول على الحقول المتاحة لمصدر بيانات
  Future<List<Map<String, dynamic>>> getDataSourceFields(String dataSource) async {
    try {
      final response = await _apiService.get('/Reports/data-sources/$dataSource/fields');
      return _apiService.handleListResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على حقول مصدر البيانات: $e');
      return [];
    }
  }

  /// معاينة بيانات التقرير
  Future<Map<String, dynamic>> previewReportData(
    String query,
    Map<String, dynamic>? parameters,
  ) async {
    try {
      final response = await _apiService.post(
        '/Reports/preview',
        body: {
          'query': query,
          'parameters': parameters ?? {},
        },
      );
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في معاينة بيانات التقرير: $e');
      return {};
    }
  }
}
