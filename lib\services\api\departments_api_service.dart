import 'package:flutter/foundation.dart';
import '../../models/department_model.dart';
import 'api_service.dart';

/// خدمة API للأقسام - متطابقة مع ASP.NET Core API
class DepartmentsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع الأقسام
  Future<List<Department>> getAllDepartments() async {
    try {
      final response = await _apiService.get('/Departments');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأقسام: $e');
      rethrow;
    }
  }

  /// الحصول على قسم بواسطة المعرف
  Future<Department?> getDepartmentById(int id) async {
    try {
      final response = await _apiService.get('/Departments/$id');
      return _apiService.handleResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على القسم $id: $e');
      return null;
    }
  }

  /// الحصول على الأقسام النشطة
  Future<List<Department>> getActiveDepartments() async {
    try {
      final response = await _apiService.get('/Departments/active');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأقسام النشطة: $e');
      rethrow;
    }
  }

  /// الحصول على الأقسام الرئيسية (بدون قسم أب)
  Future<List<Department>> getParentDepartments() async {
    try {
      final response = await _apiService.get('/Departments/parents');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأقسام الرئيسية: $e');
      rethrow;
    }
  }

  /// الحصول على الأقسام الفرعية لقسم محدد
  Future<List<Department>> getSubDepartments(int parentId) async {
    try {
      final response = await _apiService.get('/Departments/$parentId/children');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأقسام الفرعية للقسم $parentId: $e');
      rethrow;
    }
  }

  /// الحصول على موظفي قسم محدد
  Future<List<dynamic>> getDepartmentEmployees(int departmentId) async {
    try {
      final response = await _apiService.get('/Departments/$departmentId/employees');
      return _apiService.handleListResponse<dynamic>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على موظفي القسم $departmentId: $e');
      rethrow;
    }
  }

  /// الحصول على مهام قسم محدد
  Future<List<dynamic>> getDepartmentTasks(int departmentId) async {
    try {
      final response = await _apiService.get('/Departments/$departmentId/tasks');
      return _apiService.handleListResponse<dynamic>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مهام القسم $departmentId: $e');
      rethrow;
    }
  }

  /// إنشاء قسم جديد
  Future<Department> createDepartment(Department department) async {
    try {
      final response = await _apiService.post(
        '/Departments',
        body: department.toJson(),
      );
      return _apiService.handleResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء القسم: $e');
      rethrow;
    }
  }

  /// تحديث قسم
  Future<Department> updateDepartment(int id, Department department) async {
    try {
      final response = await _apiService.put(
        '/Departments/$id',
        body: department.toJson(),
      );
      return _apiService.handleResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث القسم $id: $e');
      rethrow;
    }
  }

  /// حذف قسم
  Future<bool> deleteDepartment(int id) async {
    try {
      final response = await _apiService.delete('/Departments/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف القسم $id: $e');
      return false;
    }
  }

  /// تفعيل قسم
  Future<bool> activateDepartment(int id) async {
    try {
      final response = await _apiService.put(
        '/Departments/$id/activate',
        body: {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تفعيل القسم: $e');
      return false;
    }
  }

  /// إلغاء تفعيل قسم
  Future<bool> deactivateDepartment(int id) async {
    try {
      final response = await _apiService.put(
        '/Departments/$id/deactivate',
        body: {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء تفعيل القسم: $e');
      return false;
    }
  }

  /// البحث في الأقسام
  Future<List<Department>> searchDepartments(String query) async {
    try {
      final response = await _apiService.get('/Departments/search?q=$query');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في الأقسام: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات قسم محدد
  Future<Map<String, dynamic>> getDepartmentStatistics(int departmentId) async {
    try {
      final response = await _apiService.get('/Departments/$departmentId/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات القسم: $e');
      return {};
    }
  }

  /// الحصول على إحصائيات جميع الأقسام
  Future<Map<String, dynamic>> getAllDepartmentsStatistics() async {
    try {
      final response = await _apiService.get('/Departments/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات الأقسام: $e');
      return {};
    }
  }

  /// نقل موظف إلى قسم آخر
  Future<bool> transferEmployee(int employeeId, int toDepartmentId) async {
    try {
      final response = await _apiService.put(
        '/Departments/transfer-employee',
        body: {
          'employeeId': employeeId,
          'toDepartmentId': toDepartmentId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في نقل الموظف: $e');
      return false;
    }
  }

  /// نقل مهمة إلى قسم آخر
  Future<bool> transferTask(int taskId, int toDepartmentId) async {
    try {
      final response = await _apiService.put(
        '/Departments/transfer-task',
        body: {
          'taskId': taskId,
          'toDepartmentId': toDepartmentId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في نقل المهمة: $e');
      return false;
    }
  }

  /// دمج قسمين
  Future<bool> mergeDepartments(int fromDepartmentId, int toDepartmentId) async {
    try {
      final response = await _apiService.post(
        '/Departments/merge',
        body: {
          'fromDepartmentId': fromDepartmentId,
          'toDepartmentId': toDepartmentId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في دمج الأقسام: $e');
      return false;
    }
  }

  /// تصدير بيانات الأقسام
  Future<Map<String, dynamic>> exportDepartments() async {
    try {
      final response = await _apiService.get('/Departments/export');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في تصدير بيانات الأقسام: $e');
      return {};
    }
  }

  /// استيراد بيانات الأقسام
  Future<bool> importDepartments(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.post(
        '/Departments/import',
        body: data,
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في استيراد بيانات الأقسام: $e');
      return false;
    }
  }

  /// الحصول على التسلسل الهرمي للأقسام
  Future<List<Department>> getDepartmentHierarchy() async {
    try {
      final response = await _apiService.get('/Departments/hierarchy');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على التسلسل الهرمي للأقسام: $e');
      return [];
    }
  }

  /// إعادة ترتيب الأقسام
  Future<bool> reorderDepartments(List<Map<String, dynamic>> departmentOrders) async {
    try {
      final response = await _apiService.put(
        '/Departments/reorder',
        body: {
          'departmentOrders': departmentOrders,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إعادة ترتيب الأقسام: $e');
      return false;
    }
  }
}
