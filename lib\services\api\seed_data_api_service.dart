import 'package:flutter/foundation.dart';
import 'api_service.dart';

/// خدمة API لبيانات البذور (البيانات التجريبية) - متطابقة مع ASP.NET Core API
class SeedDataApiService {
  final ApiService _apiService = ApiService();

  /// إضافة جميع البيانات التجريبية
  Future<String> seedAllData() async {
    try {
      final response = await _apiService.post('/SeedData/seed-all');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['message'] ?? 'تم إضافة جميع البيانات التجريبية بنجاح!';
    } catch (e) {
      debugPrint('خطأ في إضافة جميع البيانات التجريبية: $e');
      rethrow;
    }
  }

  /// إضافة مستخدمين تجريبيين
  Future<String> seedUsers() async {
    try {
      final response = await _apiService.post('/SeedData/seed-users');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['message'] ?? 'تم إضافة المستخدمين التجريبيين بنجاح!';
    } catch (e) {
      debugPrint('خطأ في إضافة المستخدمين التجريبيين: $e');
      rethrow;
    }
  }

  /// إضافة أقسام تجريبية
  Future<String> seedDepartments() async {
    try {
      final response = await _apiService.post('/SeedData/seed-departments');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['message'] ?? 'تم إضافة الأقسام التجريبية بنجاح!';
    } catch (e) {
      debugPrint('خطأ في إضافة الأقسام التجريبية: $e');
      rethrow;
    }
  }

  /// إضافة حالات المهام التجريبية
  Future<String> seedTaskStatuses() async {
    try {
      final response = await _apiService.post('/SeedData/seed-task-statuses');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['message'] ?? 'تم إضافة حالات المهام التجريبية بنجاح!';
    } catch (e) {
      debugPrint('خطأ في إضافة حالات المهام التجريبية: $e');
      rethrow;
    }
  }

  /// إضافة أولويات المهام التجريبية
  Future<String> seedTaskPriorities() async {
    try {
      final response = await _apiService.post('/SeedData/seed-task-priorities');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['message'] ?? 'تم إضافة أولويات المهام التجريبية بنجاح!';
    } catch (e) {
      debugPrint('خطأ في إضافة أولويات المهام التجريبية: $e');
      rethrow;
    }
  }

  /// إضافة صلاحيات تجريبية
  Future<String> seedPermissions() async {
    try {
      final response = await _apiService.post('/SeedData/seed-permissions');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['message'] ?? 'تم إضافة الصلاحيات التجريبية بنجاح!';
    } catch (e) {
      debugPrint('خطأ في إضافة الصلاحيات التجريبية: $e');
      rethrow;
    }
  }

  /// إضافة مهام تجريبية
  Future<String> seedTasks() async {
    try {
      final response = await _apiService.post('/SeedData/seed-tasks');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['message'] ?? 'تم إضافة المهام التجريبية بنجاح!';
    } catch (e) {
      debugPrint('خطأ في إضافة المهام التجريبية: $e');
      rethrow;
    }
  }

  /// إضافة مجموعات محادثة تجريبية
  Future<String> seedChatGroups() async {
    try {
      final response = await _apiService.post('/SeedData/seed-chat-groups');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['message'] ?? 'تم إضافة مجموعات المحادثة التجريبية بنجاح!';
    } catch (e) {
      debugPrint('خطأ في إضافة مجموعات المحادثة التجريبية: $e');
      rethrow;
    }
  }

  /// إضافة رسائل تجريبية
  Future<String> seedMessages() async {
    try {
      final response = await _apiService.post('/SeedData/seed-messages');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['message'] ?? 'تم إضافة الرسائل التجريبية بنجاح!';
    } catch (e) {
      debugPrint('خطأ في إضافة الرسائل التجريبية: $e');
      rethrow;
    }
  }

  /// إضافة إشعارات تجريبية
  Future<String> seedNotifications() async {
    try {
      final response = await _apiService.post('/SeedData/seed-notifications');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['message'] ?? 'تم إضافة الإشعارات التجريبية بنجاح!';
    } catch (e) {
      debugPrint('خطأ في إضافة الإشعارات التجريبية: $e');
      rethrow;
    }
  }

  /// إضافة أحداث التقويم التجريبية
  Future<String> seedCalendarEvents() async {
    try {
      final response = await _apiService.post('/SeedData/seed-calendar-events');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['message'] ?? 'تم إضافة أحداث التقويم التجريبية بنجاح!';
    } catch (e) {
      debugPrint('خطأ في إضافة أحداث التقويم التجريبية: $e');
      rethrow;
    }
  }

  /// إضافة مرفقات تجريبية
  Future<String> seedAttachments() async {
    try {
      final response = await _apiService.post('/SeedData/seed-attachments');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['message'] ?? 'تم إضافة المرفقات التجريبية بنجاح!';
    } catch (e) {
      debugPrint('خطأ في إضافة المرفقات التجريبية: $e');
      rethrow;
    }
  }

  /// مسح جميع البيانات
  Future<String> clearAllData() async {
    try {
      final response = await _apiService.post('/SeedData/clear-all');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['message'] ?? 'تم مسح جميع البيانات بنجاح!';
    } catch (e) {
      debugPrint('خطأ في مسح جميع البيانات: $e');
      rethrow;
    }
  }

  /// إعادة تعيين قاعدة البيانات
  Future<String> resetDatabase() async {
    try {
      final response = await _apiService.post('/SeedData/reset-database');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['message'] ?? 'تم إعادة تعيين قاعدة البيانات بنجاح!';
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين قاعدة البيانات: $e');
      rethrow;
    }
  }

  /// التحقق من وجود بيانات في قاعدة البيانات
  Future<Map<String, int>> checkDataStatus() async {
    try {
      final response = await _apiService.get('/SeedData/status');
      return _apiService.handleResponse<Map<String, int>>(
        response,
        (json) => Map<String, int>.from(json),
      );
    } catch (e) {
      debugPrint('خطأ في التحقق من حالة البيانات: $e');
      return {};
    }
  }

  /// الحصول على إحصائيات البيانات
  Future<Map<String, dynamic>> getDataStatistics() async {
    try {
      final response = await _apiService.get('/SeedData/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات البيانات: $e');
      return {};
    }
  }
}
