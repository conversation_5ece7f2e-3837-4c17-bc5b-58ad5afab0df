import 'user_model.dart';
import 'task_models.dart';

/// نموذج إدخال تتبع الوقت - متطابق مع ASP.NET Core API
class TimeTrackingEntry {
  final int id;
  final int taskId;
  final int userId;
  final int startTime;
  final int? endTime;
  final int? duration; // بالثواني
  final String? description;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;

  // Navigation properties
  final Task? task;
  final User? user;

  const TimeTrackingEntry({
    required this.id,
    required this.taskId,
    required this.userId,
    required this.startTime,
    this.endTime,
    this.duration,
    this.description,
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.task,
    this.user,
  });

  factory TimeTrackingEntry.fromJson(Map<String, dynamic> json) {
    return TimeTrackingEntry(
      id: json['id'] as int,
      taskId: json['taskId'] as int,
      userId: json['userId'] as int,
      startTime: json['startTime'] as int,
      endTime: json['endTime'] as int?,
      duration: json['duration'] as int?,
      description: json['description'] as String?,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      task: json['task'] != null
          ? Task.fromJson(json['task'] as Map<String, dynamic>)
          : null,
      user: json['user'] != null
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'userId': userId,
      'startTime': startTime,
      'endTime': endTime,
      'duration': duration,
      'description': description,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
    };
  }

  TimeTrackingEntry copyWith({
    int? id,
    int? taskId,
    int? userId,
    int? startTime,
    int? endTime,
    int? duration,
    String? description,
    int? createdAt,
    int? updatedAt,
    bool? isDeleted,
    Task? task,
    User? user,
  }) {
    return TimeTrackingEntry(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      userId: userId ?? this.userId,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      duration: duration ?? this.duration,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      task: task ?? this.task,
      user: user ?? this.user,
    );
  }

  /// الحصول على تاريخ البداية كـ DateTime
  DateTime get startTimeDateTime => 
      DateTime.fromMillisecondsSinceEpoch(startTime * 1000);

  /// الحصول على تاريخ النهاية كـ DateTime
  DateTime? get endTimeDateTime => endTime != null
      ? DateTime.fromMillisecondsSinceEpoch(endTime! * 1000)
      : null;

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime? get updatedAtDateTime => updatedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(updatedAt! * 1000)
      : null;

  /// التحقق من كون الجلسة نشطة (لم تنته بعد)
  bool get isActive => endTime == null;

  /// حساب المدة الفعلية
  int get actualDuration {
    if (duration != null) return duration!;
    if (endTime != null) return endTime! - startTime;
    return DateTime.now().millisecondsSinceEpoch ~/ 1000 - startTime;
  }

  /// الحصول على المدة بصيغة قابلة للقراءة
  String get durationFormatted {
    final seconds = actualDuration;
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final remainingSeconds = seconds % 60;

    if (hours > 0) {
      return '${hours}س ${minutes}د ${remainingSeconds}ث';
    } else if (minutes > 0) {
      return '${minutes}د ${remainingSeconds}ث';
    } else {
      return '${remainingSeconds}ث';
    }
  }

  @override
  String toString() {
    return 'TimeTrackingEntry(id: $id, taskId: $taskId, duration: $durationFormatted)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TimeTrackingEntry && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// تم نقل TaskProgressTracker إلى task_progress_models.dart لتجنب التكرار

/// نموذج طلب بدء تتبع الوقت
class StartTimeTrackingRequest {
  final int taskId;
  final String? description;
  final String? category;
  final bool isBillable;

  const StartTimeTrackingRequest({
    required this.taskId,
    this.description,
    this.category,
    this.isBillable = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'description': description,
      'category': category,
      'isBillable': isBillable,
    };
  }
}

/// نموذج طلب إيقاف تتبع الوقت
class StopTimeTrackingRequest {
  final String? description;

  const StopTimeTrackingRequest({
    this.description,
  });

  Map<String, dynamic> toJson() {
    return {
      'description': description,
    };
  }
}

/// نموذج طلب تسجيل تقدم المهمة
class RecordTaskProgressRequest {
  final int taskId;
  final int completionPercentage;
  final String? notes;

  const RecordTaskProgressRequest({
    required this.taskId,
    required this.completionPercentage,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'completionPercentage': completionPercentage,
      'notes': notes,
    };
  }
}
