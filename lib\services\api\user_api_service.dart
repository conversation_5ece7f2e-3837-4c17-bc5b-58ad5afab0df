import 'package:flutter/foundation.dart';
import '../../models/user_model.dart';
import 'api_service.dart';

/// خدمة API للمستخدمين
class UserApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع المستخدمين
  Future<List<User>> getAllUsers() async {
    try {
      final response = await _apiService.get('/Users');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين: $e');
      rethrow;
    }
  }

  /// الحصول على مستخدم بواسطة المعرف
  Future<User?> getUserById(int id) async {
    try {
      final response = await _apiService.get('/Users/<USER>');
      return _apiService.handleResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدم: $e');
      return null;
    }
  }

  /// إنشاء مستخدم جديد
  Future<User?> createUser(User user) async {
    try {
      final response = await _apiService.post(
        '/Users',
        body: user.toJson(),
      );
      return _apiService.handleResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء المستخدم: $e');
      rethrow;
    }
  }

  /// تحديث مستخدم
  Future<User?> updateUser(User user) async {
    try {
      final response = await _apiService.put(
        '/Users/<USER>',
        body: user.toJson(),
      );
      return _apiService.handleResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث المستخدم: $e');
      rethrow;
    }
  }

  /// حذف مستخدم (حذف ناعم)
  Future<bool> deleteUser(int id) async {
    try {
      final response = await _apiService.delete('/Users/<USER>');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف المستخدم: $e');
      return false;
    }
  }

  /// الحصول على المستخدمين النشطين
  Future<List<User>> getActiveUsers() async {
    try {
      final response = await _apiService.get('/Users/<USER>');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين النشطين: $e');
      rethrow;
    }
  }

  /// الحصول على المستخدمين المتصلين
  Future<List<User>> getOnlineUsers() async {
    try {
      final response = await _apiService.get('/Users/<USER>');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين المتصلين: $e');
      rethrow;
    }
  }

  /// الحصول على المستخدمين بدور معين
  Future<List<User>> getUsersByRole(int roleId) async {
    try {
      final response = await _apiService.get('/Users/<USER>/$roleId');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين بالدور: $e');
      rethrow;
    }
  }

  /// الحصول على المستخدمين في قسم معين
  Future<List<User>> getUsersByDepartment(int departmentId) async {
    try {
      final response = await _apiService.get('/Users/<USER>/$departmentId');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مستخدمي القسم: $e');
      rethrow;
    }
  }

  /// البحث عن المستخدمين
  Future<List<User>> searchUsers(String query) async {
    try {
      final response = await _apiService.get('/Users/<USER>');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن المستخدمين: $e');
      rethrow;
    }
  }

  /// تحديث حالة النشاط للمستخدم
  Future<bool> updateUserActiveStatus(int userId, bool isActive) async {
    try {
      final response = await _apiService.put(
        '/Users/<USER>/active',
        body: {'isActive': isActive},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة النشاط: $e');
      return false;
    }
  }

  /// تحديث حالة الاتصال للمستخدم
  Future<bool> updateUserOnlineStatus(int userId, bool isOnline) async {
    try {
      final response = await _apiService.put(
        '/Users/<USER>/online',
        body: {'isOnline': isOnline},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة الاتصال: $e');
      return false;
    }
  }

  /// تحديث دور المستخدم
  Future<bool> updateUserRole(int userId, int roleId) async {
    try {
      final response = await _apiService.put(
        '/Users/<USER>/role',
        body: {'roleId': roleId},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث دور المستخدم: $e');
      return false;
    }
  }

  /// تحديث قسم المستخدم
  Future<bool> updateUserDepartment(int userId, int? departmentId) async {
    try {
      final response = await _apiService.put(
        '/Users/<USER>/department',
        body: {'departmentId': departmentId},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث قسم المستخدم: $e');
      return false;
    }
  }

  // تم نقل طرق الأقسام إلى department_api_service.dart
}
