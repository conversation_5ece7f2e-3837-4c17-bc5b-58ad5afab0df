import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../constants/app_styles.dart';
import '../../constants/app_colors.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/theme_controller.dart';
import '../../models/user_model.dart';
import '../../widgets/app_drawer.dart';

class UserDashboardScreen extends StatefulWidget {
  const UserDashboardScreen({super.key});

  @override
  State<UserDashboardScreen> createState() => _UserDashboardScreenState();
}

class _UserDashboardScreenState extends State<UserDashboardScreen> with SingleTickerProviderStateMixin, RouteAware {
  // مراقب المسارات للتتبع عند العودة إلى الصفحة
  late RouteObserver<PageRoute> _routeObserver;
  late TabController _tabController;
  final AuthController _authController = Get.find<AuthController>();
  final TaskController _taskController = Get.find<TaskController>();

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم التبويبات
    _tabController = TabController(length: 3, vsync: this);

    // تسجيل متحكم التبويبات في GetX
    Get.put(_tabController, tag: 'user_tab_controller');

    // الحصول على مراقب المسارات من GetX
    _routeObserver = Get.find<RouteObserver<PageRoute>>();

    // تهيئة البيانات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeData();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // تسجيل هذه الصفحة مع مراقب المسارات
    final route = ModalRoute.of(context);
    if (route != null && route is PageRoute) {
      _routeObserver.subscribe(this, route);
    }
  }

  @override
  void dispose() {
    // إلغاء تسجيل هذه الصفحة من مراقب المسارات
    _routeObserver.unsubscribe(this);
    _tabController.dispose();
    super.dispose();
  }

  /// يتم استدعاء هذه الدالة عند العودة إلى هذه الصفحة
  @override
  void didPopNext() {
    debugPrint('تم العودة إلى لوحة تحكم المستخدم');
    // إعادة تحميل المهام عند العودة إلى الصفحة
    _initializeData();
  }

  /// تهيئة البيانات الأولية
  Future<void> _initializeData() async {
    // التحقق من صلاحيات المستخدم
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) {
      Get.snackbar(
        'خطأ في الصلاحيات',
        'يجب تسجيل الدخول للوصول إلى لوحة التحكم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      Get.back();
      return;
    }

    try {
      // تحميل البيانات باستخدام نظام الصلاحيات الموحد
      await _taskController.loadTasksByUserPermissions(currentUser.id);
      debugPrint('تم تحميل ${_taskController.tasks.length} مهمة في لوحة تحكم المستخدم');
    } catch (e) {
      debugPrint('خطأ في تحميل المهام: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل المهام: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // استخدام GetBuilder بدلاً من Obx لتجنب التحديثات المتكررة
    return GetBuilder<ThemeController>(
      builder: (themeController) {
        // استخدام متغيرات محلية بدلاً من الوصول المباشر للقيم المراقبة
        final isDarkMode = themeController.isDarkMode.value;

        return Directionality(
          textDirection: TextDirection.rtl, // تثبيت اتجاه النص من اليمين إلى اليسار
          child: Scaffold(
            drawer: const AppDrawer(),
            appBar: AppBar(
              title: const Text('لوحة تحكم المستخدم'),
              centerTitle: true,
              elevation: 2,
              actions: [
                // زر تبديل الوضع الليلي (Dark Mode)
                IconButton(
                  icon: Icon(
                    isDarkMode ? Icons.light_mode : Icons.dark_mode,
                  ),
                  tooltip: isDarkMode ? 'الوضع النهاري' : 'الوضع الليلي',
                  onPressed: () {
                    themeController.toggleTheme();
                  },
                ),
              ],
              bottom: _buildTabBar(),
            ),
            body: _buildBody(),
          ),
        );
      },
    );
  }

  /// بناء شريط التبويبات
  PreferredSizeWidget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      isScrollable: true,
      labelColor: Colors.white,
      unselectedLabelColor: Colors.white70,
      indicatorColor: Colors.white,
      tabs: const [
        Tab(
          icon: Icon(Icons.dashboard),
          text: 'الملخص',
        ),
        Tab(
          icon: Icon(Icons.task_alt),
          text: 'المهام المتاحة',
        ),
        Tab(
          icon: Icon(Icons.analytics),
          text: 'الإحصائيات',
        ),
      ],
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    // استخدام GetBuilder بدلاً من Obx
    return GetBuilder<AuthController>(
      builder: (authController) {
        final currentUser = authController.currentUser.value;
        if (currentUser == null) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return TabBarView(
          controller: _tabController,
          children: [
            _buildSummaryTab(currentUser),
            _buildMyTasksTab(currentUser),
            _buildStatsTab(currentUser),
          ],
        );
      },
    );
  }

  /// بناء تبويب الملخص
  Widget _buildSummaryTab(User user) {
    return Obx(() {
      final isLoading = _taskController.isLoading.value;
      final error = _taskController.error.value;

      // عرض مؤشر التحميل إذا كانت البيانات قيد التحميل
      if (isLoading) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      // عرض رسالة الخطأ إذا حدث خطأ
      if (error.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                'حدث خطأ',
                style: AppStyles.titleLarge,
              ),
              const SizedBox(height: 8),
              Text(
                error,
                style: AppStyles.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _initializeData,
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        );
      }

      return SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ترحيب بالمستخدم
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 30,
                          backgroundColor: Colors.blue.shade100,
                          backgroundImage: user.profileImage != null
                              ? NetworkImage(user.profileImage!)
                              : null,
                          child: user.profileImage == null
                              ? Icon(
                                  Icons.person,
                                  size: 30,
                                  color: Colors.blue.shade800,
                                )
                              : null,
                        ),
                        const SizedBox(width: 16),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'مرحباً، ${user.name}',
                              style: AppStyles.headingMedium,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'آخر تسجيل دخول: ${user.lastLogin != null ? _formatDate(user.lastLogin!) : 'غير متوفر'}',
                              style: AppStyles.bodySmall,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // ملخص المهام
            Text(
              'ملخص المهام',
              style: AppStyles.titleLarge,
            ),
            const SizedBox(height: 16),
            _buildTaskSummary(user),
            const SizedBox(height: 24),

            // المهام القادمة
            Text(
              'المهام القادمة',
              style: AppStyles.titleLarge,
            ),
            const SizedBox(height: 16),
            _buildUpcomingTasks(user),
          ],
        ),
      );
    });
  }

  /// بناء ملخص المهام
  Widget _buildTaskSummary(User user) {
    final tasks = _taskController.tasks;

    // تصفية المهام التي يمكن للمستخدم الوصول إليها (المخصصة له أو التي أنشأها أو في قائمة الوصول)
    final myTasks = tasks.where((task) =>
      task.assigneeId == user.id ||
      task.creatorId == user.id ||
      task.accessUserIds.contains(user.id)
    ).toList();

    final totalTasks = myTasks.length;
    final completedTasks = myTasks.where((task) => task.status == TaskStatus.completed).length;
    final inProgressTasks = myTasks.where((task) => task.status == TaskStatus.inProgress).length;
    final pendingTasks = myTasks.where((task) => task.status == TaskStatus.pending).length;

    return Row(
      children: [
        Expanded(
          child: _buildTaskCounter(
            'إجمالي المهام',
            totalTasks.toString(),
            Icons.task_alt,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildTaskCounter(
            'مكتملة',
            completedTasks.toString(),
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildTaskCounter(
            'قيد التنفيذ',
            inProgressTasks.toString(),
            Icons.pending_actions,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildTaskCounter(
            'قيد الانتظار',
            pendingTasks.toString(),
            Icons.not_started,
            Colors.red,
          ),
        ),
      ],
    );
  }

  /// بناء عداد المهام
  Widget _buildTaskCounter(String title, String count, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              count,
              style: AppStyles.headingLarge.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: AppStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة المهام القادمة
  Widget _buildUpcomingTasks(User user) {
    // تصفية المهام التي يمكن للمستخدم الوصول إليها (المخصصة له أو التي أنشأها أو في قائمة الوصول)
    final myTasks = _taskController.tasks
        .where((task) =>
            (task.assigneeId == user.id ||
             task.creatorId == user.id ||
             task.accessUserIds.contains(user.id)) &&
            task.status != TaskStatus.completed &&
            task.status != TaskStatus.cancelled)
        .toList();

    // ترتيب المهام حسب تاريخ الاستحقاق
    myTasks.sort((a, b) {
      if (a.dueDate == null && b.dueDate == null) return 0;
      if (a.dueDate == null) return 1;
      if (b.dueDate == null) return -1;
      return a.dueDate!.compareTo(b.dueDate!);
    });

    if (myTasks.isEmpty) {
      return const Card(
        elevation: 2,
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: Text('لا توجد مهام قادمة متاحة لك'),
          ),
        ),
      );
    }

    return Card(
      elevation: 2,
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: myTasks.length > 5 ? 5 : myTasks.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final task = myTasks[index];
          return ListTile(
            title: Text(task.title),
            subtitle: Text(
              task.dueDate != null
                  ? 'تاريخ الاستحقاق: ${_formatDate(task.dueDate!)}'
                  : 'بدون تاريخ استحقاق',
            ),
            leading: CircleAvatar(
              backgroundColor: _getStatusColor(task.status),
              child: _getStatusIcon(task.status),
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // الانتقال إلى تفاصيل المهمة باستخدام المسار الصحيح والمعلومات المطلوبة
              Get.toNamed('/task/detail', arguments: {'taskId': task.id});
            },
          );
        },
      ),
    );
  }

  /// بناء تبويب مهامي
  Widget _buildMyTasksTab(User user) {
    // استخدام Obx لمراقبة تغييرات المهام
    return Obx(() {
      final tasks = _taskController.tasks;
      final isLoading = _taskController.isLoading.value;
      final error = _taskController.error.value;

      // عرض مؤشر التحميل إذا كانت البيانات قيد التحميل
      if (isLoading) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      // عرض رسالة الخطأ إذا حدث خطأ
      if (error.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                'حدث خطأ',
                style: AppStyles.titleLarge,
              ),
              const SizedBox(height: 8),
              Text(
                error,
                style: AppStyles.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _initializeData,
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        );
      }

      // عرض رسالة إذا كانت قائمة المهام فارغة
      if (tasks.isEmpty) {
        return const Center(
          child: Text('لا توجد مهام متاحة'),
        );
      }

      // تصفية المهام التي يمكن للمستخدم الوصول إليها (المخصصة له أو التي أنشأها أو في قائمة الوصول)
      final myTasks = tasks.where((task) =>
        task.assigneeId == user.id ||
        task.creatorId == user.id ||
        task.accessUserIds.contains(user.id)
      ).toList();

      // عرض رسالة إذا لم تكن هناك مهام يمكن للمستخدم الوصول إليها
      if (myTasks.isEmpty) {
        return const Center(
          child: Text('لا توجد مهام يمكنك الوصول إليها'),
        );
      }

      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // أزرار التصفية
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('الكل', true),
                  const SizedBox(width: 8),
                  _buildFilterChip('قيد التنفيذ', false),
                  const SizedBox(width: 8),
                  _buildFilterChip('مكتملة', false),
                  const SizedBox(width: 8),
                  _buildFilterChip('لم تبدأ', false),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // قائمة المهام
            Expanded(
              child: ListView.builder(
                itemCount: myTasks.length,
                itemBuilder: (context, index) {
                  final task = myTasks[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      title: Text(task.title),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            task.description.length > 50
                                ? '${task.description.substring(0, 50)}...'
                                : task.description,
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.calendar_today,
                                size: 14,
                                color: Colors.grey.shade600,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                task.dueDate != null
                                    ? _formatDate(task.dueDate!)
                                    : 'بدون تاريخ استحقاق',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      leading: CircleAvatar(
                        backgroundColor: _getStatusColor(task.status),
                        child: _getStatusIcon(task.status),
                      ),
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () {
                        // الانتقال إلى تفاصيل المهمة باستخدام المسار الصحيح والمعلومات المطلوبة
                        Get.toNamed('/task/detail', arguments: {'taskId': task.id});
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      );
    });
  }

  /// بناء شريحة تصفية
  Widget _buildFilterChip(String label, bool selected) {
    return FilterChip(
      label: Text(label),
      selected: selected,
      onSelected: (value) {
        // تنفيذ التصفية
      },
      backgroundColor: Get.isDarkMode ? AppColors.darkCard : Colors.grey.shade200,
      selectedColor: Get.isDarkMode ? AppColors.primary.withAlpha(76) : Colors.blue.shade100,
      checkmarkColor: Get.isDarkMode ? Colors.white : Colors.blue.shade800,
      labelStyle: TextStyle(
        color: Get.isDarkMode ? AppColors.darkTextPrimary : null,
      ),
    );
  }

  /// بناء تبويب الإحصائيات
  Widget _buildStatsTab(User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات الأداء',
            style: AppStyles.titleLarge,
          ),
          const SizedBox(height: 16),

          // بطاقة نسبة إكمال المهام
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'نسبة إكمال المهام',
                    style: AppStyles.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  LinearProgressIndicator(
                    value: 0.7,
                    minHeight: 10,
                    backgroundColor: Colors.grey.shade200,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '70% من المهام مكتملة',
                    style: AppStyles.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // بطاقة المهام حسب الحالة
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المهام حسب الحالة',
                    style: AppStyles.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    height: 200,
                    child: Center(
                      child: Text(
                        'رسم بياني للمهام حسب الحالة',
                        style: AppStyles.bodyMedium,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // بطاقة المهام حسب الشهر
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المهام حسب الشهر',
                    style: AppStyles.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    height: 200,
                    child: Center(
                      child: Text(
                        'رسم بياني للمهام حسب الشهر',
                        style: AppStyles.bodyMedium,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    // استخدام خدمة تنسيق التاريخ العربي
    return DateFormatterService.formatDate(date);
  }

  /// الحصول على لون حالة المهمة
  Color _getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return Colors.red;
      case TaskStatus.inProgress:
        return Colors.orange;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.waitingForInfo:
        return Colors.amber;
      case TaskStatus.cancelled:
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة حالة المهمة
  Icon _getStatusIcon(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return const Icon(Icons.not_started, color: Colors.white, size: 16);
      case TaskStatus.inProgress:
        return const Icon(Icons.pending, color: Colors.white, size: 16);
      case TaskStatus.completed:
        return const Icon(Icons.check, color: Colors.white, size: 16);
      case TaskStatus.waitingForInfo:
        return const Icon(Icons.info_outline, color: Colors.white, size: 16);
      case TaskStatus.cancelled:
        return const Icon(Icons.cancel, color: Colors.white, size: 16);
      default:
        return const Icon(Icons.help, color: Colors.white, size: 16);
    }
  }
}


