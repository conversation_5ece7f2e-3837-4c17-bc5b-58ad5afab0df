import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/task_models.dart';
import '../services/api/task_history_api_service.dart';

/// متحكم تاريخ المهام
class TaskHistoryController extends GetxController {
  final TaskHistoryApiService _apiService = TaskHistoryApiService();

  // قوائم التاريخ
  final RxList<TaskHistory> _allHistory = <TaskHistory>[].obs;
  final RxList<TaskHistory> _filteredHistory = <TaskHistory>[].obs;
  final RxList<TaskHistory> _taskHistory = <TaskHistory>[].obs;

  // السجل الحالي
  final Rx<TaskHistory?> _currentHistory = Rx<TaskHistory?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<int?> _taskFilter = Rx<int?>(null);
  final Rx<String?> _actionFilter = Rx<String?>(null);
  final Rx<int?> _userFilter = Rx<int?>(null);
  final Rx<DateTime?> _dateFromFilter = Rx<DateTime?>(null);
  final Rx<DateTime?> _dateToFilter = Rx<DateTime?>(null);

  // Getters
  List<TaskHistory> get allHistory => _allHistory;
  List<TaskHistory> get filteredHistory => _filteredHistory;
  List<TaskHistory> get taskHistory => _taskHistory;
  TaskHistory? get currentHistory => _currentHistory.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  int? get taskFilter => _taskFilter.value;
  String? get actionFilter => _actionFilter.value;
  int? get userFilter => _userFilter.value;
  DateTime? get dateFromFilter => _dateFromFilter.value;
  DateTime? get dateToFilter => _dateToFilter.value;

  @override
  void onInit() {
    super.onInit();
    loadAllHistory();
  }

  /// تحميل جميع تاريخ المهام
  Future<void> loadAllHistory() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final history = await _apiService.getAllHistory();
      _allHistory.assignAll(history);
      _applyFilters();
      debugPrint('تم تحميل ${history.length} سجل تاريخ مهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل تاريخ المهام: $e';
      debugPrint('خطأ في تحميل تاريخ المهام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على سجل تاريخ مهمة بالمعرف
  Future<void> getHistoryById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final history = await _apiService.getHistoryById(id);
      _currentHistory.value = history;
      debugPrint('تم تحميل سجل تاريخ المهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل سجل تاريخ المهمة: $e';
      debugPrint('خطأ في تحميل سجل تاريخ المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء سجل تاريخ مهمة جديد
  Future<bool> createHistory(TaskHistory history) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newHistory = await _apiService.createHistory(history);
      _allHistory.insert(0, newHistory);
      _applyFilters();
      debugPrint('تم إنشاء سجل تاريخ مهمة جديد');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء سجل تاريخ المهمة: $e';
      debugPrint('خطأ في إنشاء سجل تاريخ المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف سجل تاريخ مهمة
  Future<bool> deleteHistory(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteHistory(id);
      _allHistory.removeWhere((h) => h.id == id);
      _applyFilters();
      debugPrint('تم حذف سجل تاريخ المهمة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف سجل تاريخ المهمة: $e';
      debugPrint('خطأ في حذف سجل تاريخ المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على تاريخ مهمة محددة
  Future<void> getHistoryByTask(int taskId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final history = await _apiService.getHistoryByTask(taskId);
      _taskHistory.assignAll(history);
      debugPrint('تم تحميل ${history.length} سجل تاريخ للمهمة $taskId');
    } catch (e) {
      _error.value = 'خطأ في تحميل تاريخ المهمة: $e';
      debugPrint('خطأ في تحميل تاريخ المهمة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على تاريخ المستخدم
  Future<List<TaskHistory>> getHistoryByUser(int userId) async {
    try {
      final history = await _apiService.getHistoryByUser(userId);
      debugPrint('تم تحميل ${history.length} سجل تاريخ للمستخدم $userId');
      return history;
    } catch (e) {
      debugPrint('خطأ في تحميل تاريخ المستخدم: $e');
      return [];
    }
  }

  /// الحصول على التاريخ حسب الإجراء
  Future<void> getHistoryByAction(String action) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final history = await _apiService.getHistoryByAction(action);
      _allHistory.assignAll(history);
      _applyFilters();
      debugPrint('تم تحميل ${history.length} سجل تاريخ للإجراء $action');
    } catch (e) {
      _error.value = 'خطأ في تحميل تاريخ الإجراء: $e';
      debugPrint('خطأ في تحميل تاريخ الإجراء: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على التاريخ حسب الفترة الزمنية
  Future<void> getHistoryByDateRange(DateTime from, DateTime to) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final history = await _apiService.getHistoryByDateRange(from, to);
      _allHistory.assignAll(history);
      _applyFilters();
      debugPrint('تم تحميل ${history.length} سجل تاريخ للفترة المحددة');
    } catch (e) {
      _error.value = 'خطأ في تحميل تاريخ الفترة: $e';
      debugPrint('خطأ في تحميل تاريخ الفترة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تسجيل إجراء جديد على المهمة
  Future<bool> logTaskAction(int taskId, String action, String description, Map<String, dynamic>? oldValues, Map<String, dynamic>? newValues) async {
    try {
      final history = await _apiService.logTaskAction(taskId, action, description, oldValues, newValues);
      _allHistory.insert(0, history);
      _applyFilters();
      debugPrint('تم تسجيل إجراء جديد على المهمة: $action');
      return true;
    } catch (e) {
      debugPrint('خطأ في تسجيل إجراء المهمة: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات التاريخ
  Future<Map<String, dynamic>?> getHistoryStatistics() async {
    try {
      final stats = await _apiService.getHistoryStatistics();
      debugPrint('تم تحميل إحصائيات تاريخ المهام');
      return stats;
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات تاريخ المهام: $e');
      return null;
    }
  }

  /// تصدير تاريخ المهام
  Future<String?> exportHistory(String format, DateTime? from, DateTime? to) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final filePath = await _apiService.exportHistory(format, from, to);
      debugPrint('تم تصدير تاريخ المهام: $filePath');
      return filePath;
    } catch (e) {
      _error.value = 'خطأ في تصدير تاريخ المهام: $e';
      debugPrint('خطأ في تصدير تاريخ المهام: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// مسح التاريخ القديم
  Future<bool> clearOldHistory(int daysOld) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final deletedCount = await _apiService.clearOldHistory(daysOld);
      await loadAllHistory();
      debugPrint('تم مسح $deletedCount سجل تاريخ قديم');
      return true;
    } catch (e) {
      _error.value = 'خطأ في مسح التاريخ القديم: $e';
      debugPrint('خطأ في مسح التاريخ القديم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allHistory.where((history) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!history.action.toLowerCase().contains(query) &&
            !history.description.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح المهمة
      if (_taskFilter.value != null && history.taskId != _taskFilter.value) {
        return false;
      }

      // مرشح الإجراء
      if (_actionFilter.value != null && history.action != _actionFilter.value) {
        return false;
      }

      // مرشح المستخدم
      if (_userFilter.value != null && history.userId != _userFilter.value) {
        return false;
      }

      // مرشح التاريخ من
      if (_dateFromFilter.value != null) {
        final historyDate = DateTime.fromMillisecondsSinceEpoch(history.timestamp * 1000);
        if (historyDate.isBefore(_dateFromFilter.value!)) {
          return false;
        }
      }

      // مرشح التاريخ إلى
      if (_dateToFilter.value != null) {
        final historyDate = DateTime.fromMillisecondsSinceEpoch(history.timestamp * 1000);
        if (historyDate.isAfter(_dateToFilter.value!)) {
          return false;
        }
      }

      return true;
    }).toList();

    _filteredHistory.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح المهمة
  void setTaskFilter(int? taskId) {
    _taskFilter.value = taskId;
    _applyFilters();
  }

  /// تعيين مرشح الإجراء
  void setActionFilter(String? action) {
    _actionFilter.value = action;
    _applyFilters();
  }

  /// تعيين مرشح المستخدم
  void setUserFilter(int? userId) {
    _userFilter.value = userId;
    _applyFilters();
  }

  /// تعيين مرشح التاريخ من
  void setDateFromFilter(DateTime? date) {
    _dateFromFilter.value = date;
    _applyFilters();
  }

  /// تعيين مرشح التاريخ إلى
  void setDateToFilter(DateTime? date) {
    _dateToFilter.value = date;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _taskFilter.value = null;
    _actionFilter.value = null;
    _userFilter.value = null;
    _dateFromFilter.value = null;
    _dateToFilter.value = null;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllHistory();
  }
}
