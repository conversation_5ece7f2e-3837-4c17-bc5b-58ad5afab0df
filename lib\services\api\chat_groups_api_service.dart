import 'package:flutter/foundation.dart';
import '../../models/chat_models.dart';
import 'api_service.dart';

/// خدمة API لمجموعات الدردشة - متطابقة مع ASP.NET Core API
class ChatGroupsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع مجموعات الدردشة
  Future<List<ChatGroup>> getAllGroups() async {
    try {
      final response = await _apiService.get('/ChatGroups');
      return _apiService.handleListResponse<ChatGroup>(
        response,
        (json) => ChatGroup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مجموعات الدردشة: $e');
      rethrow;
    }
  }

  /// الحصول على مجموعة دردشة بواسطة المعرف
  Future<ChatGroup?> getGroupById(int id) async {
    try {
      final response = await _apiService.get('/ChatGroups/$id');
      return _apiService.handleResponse<ChatGroup>(
        response,
        (json) => ChatGroup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مجموعة الدردشة: $e');
      return null;
    }
  }

  /// إنشاء مجموعة دردشة جديدة
  Future<ChatGroup?> createGroup(ChatGroup group) async {
    try {
      final response = await _apiService.post(
        '/ChatGroups',
        body: group.toJson(),
      );
      return _apiService.handleResponse<ChatGroup>(
        response,
        (json) => ChatGroup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء مجموعة الدردشة: $e');
      rethrow;
    }
  }

  /// تحديث مجموعة دردشة
  Future<ChatGroup?> updateGroup(ChatGroup group) async {
    try {
      final response = await _apiService.put(
        '/ChatGroups/${group.id}',
        body: group.toJson(),
      );
      return _apiService.handleResponse<ChatGroup>(
        response,
        (json) => ChatGroup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث مجموعة الدردشة: $e');
      rethrow;
    }
  }

  /// حذف مجموعة دردشة
  Future<bool> deleteGroup(int id) async {
    try {
      final response = await _apiService.delete('/ChatGroups/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف مجموعة الدردشة: $e');
      return false;
    }
  }

  /// الحصول على مجموعات المستخدم
  Future<List<ChatGroup>> getUserGroups(int userId) async {
    try {
      final response = await _apiService.get('/ChatGroups/user/$userId');
      return _apiService.handleListResponse<ChatGroup>(
        response,
        (json) => ChatGroup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مجموعات المستخدم: $e');
      rethrow;
    }
  }

  /// البحث في مجموعات الدردشة
  Future<List<ChatGroup>> searchGroups(String query) async {
    try {
      final response = await _apiService.get('/ChatGroups/search?searchTerm=$query');
      return _apiService.handleListResponse<ChatGroup>(
        response,
        (json) => ChatGroup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن مجموعات الدردشة: $e');
      rethrow;
    }
  }

  /// الحصول على المجموعات العامة
  Future<List<ChatGroup>> getPublicGroups() async {
    try {
      final response = await _apiService.get('/ChatGroups/public');
      return _apiService.handleListResponse<ChatGroup>(
        response,
        (json) => ChatGroup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المجموعات العامة: $e');
      rethrow;
    }
  }

  /// الحصول على المجموعات الخاصة
  Future<List<ChatGroup>> getPrivateGroups(int userId) async {
    try {
      final response = await _apiService.get('/ChatGroups/private/$userId');
      return _apiService.handleListResponse<ChatGroup>(
        response,
        (json) => ChatGroup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المجموعات الخاصة: $e');
      rethrow;
    }
  }

  /// إضافة عضو لمجموعة
  Future<bool> addMemberToGroup(int groupId, int userId) async {
    try {
      final response = await _apiService.post(
        '/ChatGroups/$groupId/members',
        body: {'userId': userId},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إضافة عضو للمجموعة: $e');
      return false;
    }
  }

  /// إزالة عضو من مجموعة
  Future<bool> removeMemberFromGroup(int groupId, int userId) async {
    try {
      final response = await _apiService.delete('/ChatGroups/$groupId/members/$userId');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إزالة عضو من المجموعة: $e');
      return false;
    }
  }

  /// الحصول على أعضاء المجموعة
  Future<List<Map<String, dynamic>>> getGroupMembers(int groupId) async {
    try {
      final response = await _apiService.get('/ChatGroups/$groupId/members');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return List<Map<String, dynamic>>.from(response.body as List);
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على أعضاء المجموعة: $e');
      return [];
    }
  }

  /// تحديث دور عضو في المجموعة
  Future<bool> updateMemberRole(int groupId, int userId, String role) async {
    try {
      final response = await _apiService.put(
        '/ChatGroups/$groupId/members/$userId/role',
        body: {'role': role},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث دور العضو: $e');
      return false;
    }
  }

  /// مغادرة المجموعة
  Future<bool> leaveGroup(int groupId, int userId) async {
    try {
      final response = await _apiService.delete('/ChatGroups/$groupId/leave/$userId');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في مغادرة المجموعة: $e');
      return false;
    }
  }

  /// الانضمام للمجموعة
  Future<bool> joinGroup(int groupId, int userId) async {
    try {
      final response = await _apiService.post(
        '/ChatGroups/$groupId/join',
        body: {'userId': userId},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في الانضمام للمجموعة: $e');
      return false;
    }
  }

  /// تحديث إعدادات المجموعة
  Future<bool> updateGroupSettings(int groupId, Map<String, dynamic> settings) async {
    try {
      final response = await _apiService.put(
        '/ChatGroups/$groupId/settings',
        body: settings,
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث إعدادات المجموعة: $e');
      return false;
    }
  }

  /// الحصول على إعدادات المجموعة
  Future<Map<String, dynamic>?> getGroupSettings(int groupId) async {
    try {
      final response = await _apiService.get('/ChatGroups/$groupId/settings');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return Map<String, dynamic>.from(response.body as Map);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على إعدادات المجموعة: $e');
      return null;
    }
  }

  /// تحديث صورة المجموعة
  Future<bool> updateGroupImage(int groupId, String imageUrl) async {
    try {
      final response = await _apiService.put(
        '/ChatGroups/$groupId/image',
        body: {'imageUrl': imageUrl},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث صورة المجموعة: $e');
      return false;
    }
  }

  /// أرشفة المجموعة
  Future<bool> archiveGroup(int groupId) async {
    try {
      final response = await _apiService.put(
        '/ChatGroups/$groupId/archive',
        body: {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في أرشفة المجموعة: $e');
      return false;
    }
  }

  /// إلغاء أرشفة المجموعة
  Future<bool> unarchiveGroup(int groupId) async {
    try {
      final response = await _apiService.put(
        '/ChatGroups/$groupId/unarchive',
        body: {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء أرشفة المجموعة: $e');
      return false;
    }
  }

  /// الحصول على المجموعات المؤرشفة
  Future<List<ChatGroup>> getArchivedGroups(int userId) async {
    try {
      final response = await _apiService.get('/ChatGroups/archived/$userId');
      return _apiService.handleListResponse<ChatGroup>(
        response,
        (json) => ChatGroup.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المجموعات المؤرشفة: $e');
      rethrow;
    }
  }

  /// الحصول على إحصائيات المجموعة
  Future<Map<String, dynamic>?> getGroupStatistics(int groupId) async {
    try {
      final response = await _apiService.get('/ChatGroups/$groupId/statistics');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return Map<String, dynamic>.from(response.body as Map);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات المجموعة: $e');
      return null;
    }
  }

  /// تصدير بيانات المجموعة
  Future<String?> exportGroupData(int groupId, String format) async {
    try {
      final response = await _apiService.get('/ChatGroups/$groupId/export?format=$format');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تصدير بيانات المجموعة: $e');
      return null;
    }
  }
}
