import 'package:flutter/foundation.dart';
import '../../models/task_comment_models.dart';
import 'api_service.dart';

/// خدمة API لتعليقات المهام - متطابقة مع ASP.NET Core API
class TaskCommentsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع تعليقات المهام
  Future<List<TaskComment>> getAllTaskComments() async {
    try {
      final response = await _apiService.get('/TaskComments');
      return _apiService.handleListResponse<TaskComment>(
        response,
        (json) => TaskComment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على تعليقات المهام: $e');
      rethrow;
    }
  }

  /// الحصول على تعليق مهمة بواسطة المعرف
  Future<TaskComment?> getTaskCommentById(int id) async {
    try {
      final response = await _apiService.get('/TaskComments/$id');
      return _apiService.handleResponse<TaskComment>(
        response,
        (json) => TaskComment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على تعليق المهمة $id: $e');
      return null;
    }
  }

  /// الحصول على تعليقات مهمة محددة
  Future<List<TaskComment>> getCommentsByTask(int taskId) async {
    try {
      final response = await _apiService.get('/TaskComments/task/$taskId');
      return _apiService.handleListResponse<TaskComment>(
        response,
        (json) => TaskComment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على تعليقات المهمة $taskId: $e');
      rethrow;
    }
  }

  /// الحصول على تعليقات مستخدم محدد
  Future<List<TaskComment>> getCommentsByUser(int userId) async {
    try {
      final response = await _apiService.get('/TaskComments/user/$userId');
      return _apiService.handleListResponse<TaskComment>(
        response,
        (json) => TaskComment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على تعليقات المستخدم $userId: $e');
      rethrow;
    }
  }

  /// الحصول على التعليقات الحديثة
  Future<List<TaskComment>> getRecentComments({int limit = 10}) async {
    try {
      final response = await _apiService.get('/TaskComments/recent?limit=$limit');
      return _apiService.handleListResponse<TaskComment>(
        response,
        (json) => TaskComment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على التعليقات الحديثة: $e');
      return [];
    }
  }

  /// إنشاء تعليق جديد على مهمة
  Future<TaskComment> createTaskComment(TaskComment comment) async {
    try {
      final response = await _apiService.post(
        '/TaskComments',
        body: comment.toJson(),
      );
      return _apiService.handleResponse<TaskComment>(
        response,
        (json) => TaskComment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء تعليق المهمة: $e');
      rethrow;
    }
  }

  /// تحديث تعليق مهمة
  Future<TaskComment> updateTaskComment(int id, TaskComment comment) async {
    try {
      final response = await _apiService.put(
        '/TaskComments/$id',
        body: comment.toJson(),
      );
      return _apiService.handleResponse<TaskComment>(
        response,
        (json) => TaskComment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث تعليق المهمة $id: $e');
      rethrow;
    }
  }

  /// حذف تعليق مهمة
  Future<bool> deleteTaskComment(int id) async {
    try {
      final response = await _apiService.delete('/TaskComments/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف تعليق المهمة $id: $e');
      return false;
    }
  }

  /// الإعجاب بتعليق
  Future<bool> likeComment(int commentId) async {
    try {
      final response = await _apiService.post(
        '/TaskComments/$commentId/like',
        body: {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في الإعجاب بالتعليق: $e');
      return false;
    }
  }

  /// إلغاء الإعجاب بتعليق
  Future<bool> unlikeComment(int commentId) async {
    try {
      final response = await _apiService.delete('/TaskComments/$commentId/like');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء الإعجاب بالتعليق: $e');
      return false;
    }
  }

  /// الرد على تعليق
  Future<TaskComment> replyToComment(int parentCommentId, TaskComment reply) async {
    try {
      final response = await _apiService.post(
        '/TaskComments/$parentCommentId/reply',
        body: reply.toJson(),
      );
      return _apiService.handleResponse<TaskComment>(
        response,
        (json) => TaskComment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الرد على التعليق: $e');
      rethrow;
    }
  }

  /// الحصول على ردود تعليق محدد
  Future<List<TaskComment>> getCommentReplies(int commentId) async {
    try {
      final response = await _apiService.get('/TaskComments/$commentId/replies');
      return _apiService.handleListResponse<TaskComment>(
        response,
        (json) => TaskComment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على ردود التعليق $commentId: $e');
      return [];
    }
  }

  /// البحث في التعليقات
  Future<List<TaskComment>> searchComments(String query) async {
    try {
      final response = await _apiService.get('/TaskComments/search?q=$query');
      return _apiService.handleListResponse<TaskComment>(
        response,
        (json) => TaskComment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في التعليقات: $e');
      return [];
    }
  }

  /// تحديد تعليق كمهم
  Future<bool> markCommentAsImportant(int commentId) async {
    try {
      final response = await _apiService.put(
        '/TaskComments/$commentId/mark-important',
        body: {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديد التعليق كمهم: $e');
      return false;
    }
  }

  /// إلغاء تحديد تعليق كمهم
  Future<bool> unmarkCommentAsImportant(int commentId) async {
    try {
      final response = await _apiService.put(
        '/TaskComments/$commentId/unmark-important',
        body: {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء تحديد التعليق كمهم: $e');
      return false;
    }
  }

  /// الحصول على التعليقات المهمة لمهمة محددة
  Future<List<TaskComment>> getImportantComments(int taskId) async {
    try {
      final response = await _apiService.get('/TaskComments/task/$taskId/important');
      return _apiService.handleListResponse<TaskComment>(
        response,
        (json) => TaskComment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على التعليقات المهمة: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات التعليقات لمهمة محددة
  Future<Map<String, dynamic>> getCommentStatistics(int taskId) async {
    try {
      final response = await _apiService.get('/TaskComments/task/$taskId/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات التعليقات: $e');
      return {};
    }
  }

  /// حذف جميع تعليقات مهمة محددة
  Future<bool> deleteAllTaskComments(int taskId) async {
    try {
      final response = await _apiService.delete('/TaskComments/task/$taskId');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف جميع تعليقات المهمة: $e');
      return false;
    }
  }

  /// تصدير تعليقات مهمة محددة
  Future<Map<String, dynamic>> exportTaskComments(int taskId) async {
    try {
      final response = await _apiService.get('/TaskComments/task/$taskId/export');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في تصدير تعليقات المهمة: $e');
      return {};
    }
  }

  /// الحصول على التعليقات بحسب التاريخ
  Future<List<TaskComment>> getCommentsByDateRange(
    int taskId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final startTimestamp = startDate.millisecondsSinceEpoch ~/ 1000;
      final endTimestamp = endDate.millisecondsSinceEpoch ~/ 1000;
      
      final response = await _apiService.get(
        '/TaskComments/task/$taskId/date-range?start=$startTimestamp&end=$endTimestamp',
      );
      return _apiService.handleListResponse<TaskComment>(
        response,
        (json) => TaskComment.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على التعليقات بحسب التاريخ: $e');
      return [];
    }
  }
}
