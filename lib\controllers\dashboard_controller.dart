import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// متحكم لوحة المعلومات الرئيسية
class DashboardController extends GetxController {
  // المتغيرات التفاعلية
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  final RxMap<String, dynamic> _dashboardData = <String, dynamic>{}.obs;
  final RxList<Map<String, dynamic>> _recentActivities = <Map<String, dynamic>>[].obs;
  final RxMap<String, int> _statistics = <String, int>{}.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  Map<String, dynamic> get dashboardData => _dashboardData;
  List<Map<String, dynamic>> get recentActivities => _recentActivities;
  Map<String, int> get statistics => _statistics;

  @override
  void onInit() {
    super.onInit();
    loadDashboardData();
  }

  /// تحميل بيانات لوحة المعلومات
  Future<void> loadDashboardData() async {
    try {
      _isLoading.value = true;
      _error.value = '';

      // محاكاة تحميل البيانات
      await Future.delayed(const Duration(seconds: 1));

      // بيانات وهمية للاختبار
      _dashboardData.value = {
        'totalTasks': 150,
        'completedTasks': 85,
        'pendingTasks': 45,
        'overdueTasks': 20,
        'totalProjects': 12,
        'activeProjects': 8,
        'totalUsers': 25,
        'activeUsers': 18,
      };

      _statistics.value = {
        'tasks': 150,
        'projects': 12,
        'users': 25,
        'departments': 5,
      };

      _recentActivities.value = [
        {
          'id': 1,
          'type': 'task_completed',
          'title': 'تم إكمال مهمة تطوير الواجهة',
          'user': 'أحمد محمد',
          'timestamp': DateTime.now().subtract(const Duration(minutes: 30)),
        },
        {
          'id': 2,
          'type': 'project_created',
          'title': 'تم إنشاء مشروع جديد',
          'user': 'فاطمة علي',
          'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
        },
        {
          'id': 3,
          'type': 'user_joined',
          'title': 'انضم مستخدم جديد للفريق',
          'user': 'محمد أحمد',
          'timestamp': DateTime.now().subtract(const Duration(hours: 4)),
        },
      ];

      debugPrint('تم تحميل بيانات لوحة المعلومات بنجاح');
    } catch (e) {
      _error.value = 'خطأ في تحميل بيانات لوحة المعلومات: $e';
      debugPrint('خطأ في تحميل بيانات لوحة المعلومات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث البيانات
  Future<void> refreshData() async {
    await loadDashboardData();
  }

  /// الحصول على نسبة إكمال المهام
  double get taskCompletionRate {
    final total = _dashboardData['totalTasks'] ?? 0;
    final completed = _dashboardData['completedTasks'] ?? 0;
    return total > 0 ? (completed / total) * 100 : 0.0;
  }

  /// الحصول على نسبة المشاريع النشطة
  double get activeProjectsRate {
    final total = _dashboardData['totalProjects'] ?? 0;
    final active = _dashboardData['activeProjects'] ?? 0;
    return total > 0 ? (active / total) * 100 : 0.0;
  }

  /// الحصول على نسبة المستخدمين النشطين
  double get activeUsersRate {
    final total = _dashboardData['totalUsers'] ?? 0;
    final active = _dashboardData['activeUsers'] ?? 0;
    return total > 0 ? (active / total) * 100 : 0.0;
  }

  /// إضافة نشاط جديد
  void addActivity(Map<String, dynamic> activity) {
    _recentActivities.insert(0, activity);
    // الاحتفاظ بآخر 10 أنشطة فقط
    if (_recentActivities.length > 10) {
      _recentActivities.removeRange(10, _recentActivities.length);
    }
  }

  /// تحديث إحصائية معينة
  void updateStatistic(String key, int value) {
    _statistics[key] = value;
    _dashboardData[key] = value;
  }

  /// مسح الأخطاء
  void clearError() {
    _error.value = '';
  }

  /// الحصول على لون حالة المهام
  Color getTaskStatusColor() {
    final overdue = _dashboardData['overdueTasks'] ?? 0;
    final total = _dashboardData['totalTasks'] ?? 0;
    
    if (total == 0) return Colors.grey;
    
    final overdueRate = (overdue / total) * 100;
    if (overdueRate > 20) return Colors.red;
    if (overdueRate > 10) return Colors.orange;
    return Colors.green;
  }

  /// الحصول على رسالة حالة المهام
  String getTaskStatusMessage() {
    final overdue = _dashboardData['overdueTasks'] ?? 0;
    final pending = _dashboardData['pendingTasks'] ?? 0;
    final completed = _dashboardData['completedTasks'] ?? 0;
    
    if (overdue > 0) {
      return 'لديك $overdue مهمة متأخرة تحتاج إلى اهتمام';
    } else if (pending > 0) {
      return 'لديك $pending مهمة معلقة';
    } else if (completed > 0) {
      return 'ممتاز! تم إكمال جميع المهام';
    } else {
      return 'لا توجد مهام حالياً';
    }
  }

  /// الحصول على أيقونة حالة المهام
  IconData getTaskStatusIcon() {
    final overdue = _dashboardData['overdueTasks'] ?? 0;
    final pending = _dashboardData['pendingTasks'] ?? 0;
    
    if (overdue > 0) {
      return Icons.warning;
    } else if (pending > 0) {
      return Icons.pending_actions;
    } else {
      return Icons.check_circle;
    }
  }

  /// تصفية الأنشطة حسب النوع
  List<Map<String, dynamic>> getActivitiesByType(String type) {
    return _recentActivities.where((activity) => activity['type'] == type).toList();
  }

  /// الحصول على أنواع الأنشطة المختلفة
  List<String> get activityTypes {
    return _recentActivities
        .map((activity) => activity['type'] as String)
        .toSet()
        .toList();
  }

  /// تحديث بيانات محددة
  void updateDashboardData(String key, dynamic value) {
    _dashboardData[key] = value;
  }

  /// إعادة تعيين البيانات
  void resetData() {
    _dashboardData.clear();
    _recentActivities.clear();
    _statistics.clear();
    _error.value = '';
  }
}
