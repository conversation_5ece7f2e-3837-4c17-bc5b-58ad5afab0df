import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../models/dashboard_model.dart';
import '../models/user_model.dart';
import 'storage_service.dart';

/// خدمة لوحة المعلومات
class DashboardService extends GetxService {
  final StorageService _storageService = Get.find<StorageService>();

  // لوحة المعلومات الحالية
  final Rx<Dashboard?> _currentDashboard = Rx<Dashboard?>(null);
  
  // قائمة لوحات المعلومات
  final RxList<Dashboard> _dashboards = <Dashboard>[].obs;
  
  // حالة التحميل
  final RxBool _isLoading = false.obs;
  
  // رسالة الخطأ
  final RxString _error = ''.obs;

  // Getters
  Dashboard? get currentDashboard => _currentDashboard.value;
  List<Dashboard> get dashboards => _dashboards;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  
  // Observable getters
  Rx<Dashboard?> get currentDashboardObs => _currentDashboard;
  RxList<Dashboard> get dashboardsObs => _dashboards;

  /// تهيئة الخدمة
  Future<DashboardService> init() async {
    await loadDashboards();
    return this;
  }

  /// تحميل لوحات المعلومات
  Future<void> loadDashboards() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // تحميل لوحات المعلومات من التخزين المحلي
      final dashboardsData = await _storageService.getDashboards();
      _dashboards.assignAll(dashboardsData);

      // تحديد لوحة المعلومات الافتراضية
      if (_dashboards.isNotEmpty && _currentDashboard.value == null) {
        _currentDashboard.value = _dashboards.first;
      }

      debugPrint('تم تحميل ${_dashboards.length} لوحة معلومات');
    } catch (e) {
      _error.value = 'خطأ في تحميل لوحات المعلومات: $e';
      debugPrint('خطأ في تحميل لوحات المعلومات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء لوحة معلومات جديدة
  Future<Dashboard> createDashboard({
    required String title,
    String? description,
    List<DashboardWidget>? widgets,
  }) async {
    try {
      final dashboard = Dashboard(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        description: description ?? '',
        widgets: widgets ?? [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      _dashboards.add(dashboard);
      await _saveDashboards();

      // تعيين كلوحة افتراضية إذا كانت الأولى
      if (_dashboards.length == 1) {
        _currentDashboard.value = dashboard;
      }

      debugPrint('تم إنشاء لوحة معلومات جديدة: $title');
      return dashboard;
    } catch (e) {
      _error.value = 'خطأ في إنشاء لوحة المعلومات: $e';
      debugPrint('خطأ في إنشاء لوحة المعلومات: $e');
      rethrow;
    }
  }

  /// تحديث لوحة معلومات
  Future<void> updateDashboard(Dashboard dashboard) async {
    try {
      final index = _dashboards.indexWhere((d) => d.id == dashboard.id);
      if (index != -1) {
        final updatedDashboard = dashboard.copyWith(updatedAt: DateTime.now());
        _dashboards[index] = updatedDashboard;
        
        // تحديث لوحة المعلومات الحالية إذا كانت نفسها
        if (_currentDashboard.value?.id == dashboard.id) {
          _currentDashboard.value = updatedDashboard;
        }
        
        await _saveDashboards();
        debugPrint('تم تحديث لوحة المعلومات: ${dashboard.title}');
      }
    } catch (e) {
      _error.value = 'خطأ في تحديث لوحة المعلومات: $e';
      debugPrint('خطأ في تحديث لوحة المعلومات: $e');
    }
  }

  /// حذف لوحة معلومات
  Future<void> deleteDashboard(String dashboardId) async {
    try {
      _dashboards.removeWhere((d) => d.id == dashboardId);
      
      // إذا كانت لوحة المعلومات المحذوفة هي الحالية، اختر أخرى
      if (_currentDashboard.value?.id == dashboardId) {
        _currentDashboard.value = _dashboards.isNotEmpty ? _dashboards.first : null;
      }
      
      await _saveDashboards();
      debugPrint('تم حذف لوحة المعلومات: $dashboardId');
    } catch (e) {
      _error.value = 'خطأ في حذف لوحة المعلومات: $e';
      debugPrint('خطأ في حذف لوحة المعلومات: $e');
    }
  }

  /// تعيين لوحة المعلومات الحالية
  void setCurrentDashboard(String dashboardId) {
    final dashboard = _dashboards.firstWhereOrNull((d) => d.id == dashboardId);
    if (dashboard != null) {
      _currentDashboard.value = dashboard;
      debugPrint('تم تعيين لوحة المعلومات الحالية: ${dashboard.title}');
    }
  }

  /// إضافة عنصر إلى لوحة المعلومات
  Future<void> addWidget(String dashboardId, DashboardWidget widget) async {
    try {
      final dashboard = _dashboards.firstWhereOrNull((d) => d.id == dashboardId);
      if (dashboard != null) {
        final updatedWidgets = List<DashboardWidget>.from(dashboard.widgets)..add(widget);
        final updatedDashboard = dashboard.copyWith(
          widgets: updatedWidgets,
          updatedAt: DateTime.now(),
        );
        await updateDashboard(updatedDashboard);
        debugPrint('تم إضافة عنصر جديد إلى لوحة المعلومات');
      }
    } catch (e) {
      _error.value = 'خطأ في إضافة العنصر: $e';
      debugPrint('خطأ في إضافة العنصر: $e');
    }
  }

  /// تحديث موقع عنصر
  Future<void> updateWidgetPosition(String widgetId, int row, int column) async {
    try {
      final dashboard = _currentDashboard.value;
      if (dashboard != null) {
        final updatedWidgets = dashboard.widgets.map((w) {
          if (w.id == widgetId) {
            return w.copyWith(row: row, column: column);
          }
          return w;
        }).toList();
        
        final updatedDashboard = dashboard.copyWith(
          widgets: updatedWidgets,
          updatedAt: DateTime.now(),
        );
        await updateDashboard(updatedDashboard);
      }
    } catch (e) {
      _error.value = 'خطأ في تحديث موقع العنصر: $e';
      debugPrint('خطأ في تحديث موقع العنصر: $e');
    }
  }

  /// تحديث إعدادات عنصر
  Future<void> updateWidgetSettings(String widgetId, Map<String, dynamic> settings) async {
    try {
      final dashboard = _currentDashboard.value;
      if (dashboard != null) {
        final updatedWidgets = dashboard.widgets.map((w) {
          if (w.id == widgetId) {
            return w.copyWith(settings: settings);
          }
          return w;
        }).toList();
        
        final updatedDashboard = dashboard.copyWith(
          widgets: updatedWidgets,
          updatedAt: DateTime.now(),
        );
        await updateDashboard(updatedDashboard);
      }
    } catch (e) {
      _error.value = 'خطأ في تحديث إعدادات العنصر: $e';
      debugPrint('خطأ في تحديث إعدادات العنصر: $e');
    }
  }

  /// حفظ لوحة معلومات
  Future<void> saveDashboard(Dashboard dashboard) async {
    await updateDashboard(dashboard);
  }

  /// حفظ جميع لوحات المعلومات
  Future<void> _saveDashboards() async {
    try {
      await _storageService.saveDashboards(_dashboards);
    } catch (e) {
      debugPrint('خطأ في حفظ لوحات المعلومات: $e');
    }
  }

  /// إنشاء لوحة معلومات افتراضية
  Future<void> createDefaultDashboard() async {
    if (_dashboards.isEmpty) {
      await createDashboard(
        title: 'لوحة المعلومات الرئيسية',
        description: 'لوحة المعلومات الافتراضية',
        widgets: _getDefaultWidgets(),
      );
    }
  }

  /// الحصول على العناصر الافتراضية
  List<DashboardWidget> _getDefaultWidgets() {
    return [
      DashboardWidget(
        id: 'tasks_summary',
        type: 'chart',
        title: 'ملخص المهام',
        row: 0,
        column: 0,
        width: 2,
        height: 1,
        settings: {'chartType': 'pie', 'dataSource': 'tasks'},
      ),
      DashboardWidget(
        id: 'recent_activities',
        type: 'list',
        title: 'الأنشطة الحديثة',
        row: 0,
        column: 2,
        width: 2,
        height: 2,
        settings: {'dataSource': 'activities', 'limit': 10},
      ),
    ];
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }
}
