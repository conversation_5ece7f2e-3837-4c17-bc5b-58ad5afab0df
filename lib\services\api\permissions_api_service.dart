import 'package:flutter/foundation.dart';
import '../../models/permission_model.dart';
import '../../models/user_model.dart';
import 'api_service.dart';

/// خدمة API للصلاحيات - متطابقة مع ASP.NET Core API
class PermissionsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع الصلاحيات
  Future<List<Permission>> getAllPermissions() async {
    try {
      final response = await _apiService.get('/Permissions');
      return _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الصلاحيات: $e');
      rethrow;
    }
  }

  /// الحصول على صلاحية بواسطة المعرف
  Future<Permission?> getPermissionById(int id) async {
    try {
      final response = await _apiService.get('/Permissions/$id');
      return _apiService.handleResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الصلاحية $id: $e');
      return null;
    }
  }

  /// الحصول على الصلاحيات بحسب المجموعة
  Future<List<Permission>> getPermissionsByGroup(String group) async {
    try {
      final response = await _apiService.get('/Permissions/group/$group');
      return _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على صلاحيات المجموعة $group: $e');
      rethrow;
    }
  }

  /// الحصول على جميع مجموعات الصلاحيات
  Future<List<String>> getPermissionGroups() async {
    try {
      final response = await _apiService.get('/Permissions/groups');
      return _apiService.handleListResponse<String>(
        response,
        (json) => json as String,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مجموعات الصلاحيات: $e');
      return [];
    }
  }

  /// الحصول على المستخدمين الذين لديهم صلاحية محددة
  Future<List<User>> getUsersWithPermission(int permissionId) async {
    try {
      final response = await _apiService.get('/Permissions/$permissionId/users');
      return _apiService.handleListResponse<User>(
        response,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين بالصلاحية $permissionId: $e');
      rethrow;
    }
  }

  /// إنشاء صلاحية جديدة
  Future<Permission> createPermission(Permission permission) async {
    try {
      final response = await _apiService.post(
        '/Permissions',
        body: permission.toJson(),
      );
      return _apiService.handleResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء الصلاحية: $e');
      rethrow;
    }
  }

  /// تحديث صلاحية
  Future<Permission> updatePermission(int id, Permission permission) async {
    try {
      final response = await _apiService.put(
        '/Permissions/$id',
        body: permission.toJson(),
      );
      return _apiService.handleResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث الصلاحية $id: $e');
      rethrow;
    }
  }

  /// حذف صلاحية
  Future<bool> deletePermission(int id) async {
    try {
      final response = await _apiService.delete('/Permissions/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف الصلاحية $id: $e');
      return false;
    }
  }

  /// التحقق من صلاحية مستخدم
  Future<bool> checkUserPermission(int userId, String permissionName) async {
    try {
      final response = await _apiService.get('/Permissions/check/$userId/$permissionName');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['hasPermission'] as bool? ?? false;
    } catch (e) {
      debugPrint('خطأ في التحقق من صلاحية المستخدم: $e');
      return false;
    }
  }

  /// التحقق من صلاحية المستخدم الحالي
  Future<bool> checkPermission(String permissionName) async {
    try {
      final response = await _apiService.get('/Permissions/check/$permissionName');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return data['hasPermission'] as bool? ?? false;
    } catch (e) {
      debugPrint('خطأ في التحقق من الصلاحية: $e');
      return false;
    }
  }

  /// منح صلاحية لدور
  Future<bool> grantPermissionToRole(String role, int permissionId) async {
    try {
      final response = await _apiService.post(
        '/Permissions/grant-to-role',
        body: {
          'role': role,
          'permissionId': permissionId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في منح الصلاحية للدور: $e');
      return false;
    }
  }

  /// إلغاء صلاحية من دور
  Future<bool> revokePermissionFromRole(String role, int permissionId) async {
    try {
      final response = await _apiService.post(
        '/Permissions/revoke-from-role',
        body: {
          'role': role,
          'permissionId': permissionId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء الصلاحية من الدور: $e');
      return false;
    }
  }

  /// الحصول على صلاحيات دور محدد
  Future<List<Permission>> getRolePermissions(String role) async {
    try {
      final response = await _apiService.get('/Permissions/role/$role');
      return _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على صلاحيات الدور $role: $e');
      return [];
    }
  }

  /// البحث في الصلاحيات
  Future<List<Permission>> searchPermissions(String query) async {
    try {
      final response = await _apiService.get('/Permissions/search?q=$query');
      return _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في الصلاحيات: $e');
      return [];
    }
  }

  /// الحصول على الصلاحيات بحسب المستوى
  Future<List<Permission>> getPermissionsByLevel(int level) async {
    try {
      final response = await _apiService.get('/Permissions/level/$level');
      return _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الصلاحيات بالمستوى $level: $e');
      return [];
    }
  }

  /// الحصول على الصلاحيات الافتراضية
  Future<List<Permission>> getDefaultPermissions() async {
    try {
      final response = await _apiService.get('/Permissions/defaults');
      return _apiService.handleListResponse<Permission>(
        response,
        (json) => Permission.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الصلاحيات الافتراضية: $e');
      return [];
    }
  }

  /// تصدير الصلاحيات
  Future<Map<String, dynamic>> exportPermissions() async {
    try {
      final response = await _apiService.get('/Permissions/export');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في تصدير الصلاحيات: $e');
      return {};
    }
  }

  /// استيراد الصلاحيات
  Future<bool> importPermissions(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.post(
        '/Permissions/import',
        body: data,
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في استيراد الصلاحيات: $e');
      return false;
    }
  }
}
