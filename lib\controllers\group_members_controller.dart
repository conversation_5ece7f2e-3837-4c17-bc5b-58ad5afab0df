import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/chat_models.dart';
import '../services/api/group_members_api_service.dart';

/// متحكم أعضاء المجموعات
class GroupMembersController extends GetxController {
  final GroupMembersApiService _apiService = GroupMembersApiService();

  // قوائم الأعضاء
  final RxList<GroupMember> _allMembers = <GroupMember>[].obs;
  final RxList<GroupMember> _filteredMembers = <GroupMember>[].obs;
  final RxList<GroupMember> _groupMembers = <GroupMember>[].obs;

  // العضو الحالي
  final Rx<GroupMember?> _currentMember = Rx<GroupMember?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<int?> _groupFilter = Rx<int?>(null);
  final Rx<String?> _roleFilter = Rx<String?>(null);
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<GroupMember> get allMembers => _allMembers;
  List<GroupMember> get filteredMembers => _filteredMembers;
  List<GroupMember> get groupMembers => _groupMembers;
  GroupMember? get currentMember => _currentMember.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  int? get groupFilter => _groupFilter.value;
  String? get roleFilter => _roleFilter.value;
  bool get showActiveOnly => _showActiveOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllMembers();
  }

  /// تحميل جميع أعضاء المجموعات
  Future<void> loadAllMembers() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final members = await _apiService.getAllMembers();
      _allMembers.assignAll(members);
      _applyFilters();
      debugPrint('تم تحميل ${members.length} عضو مجموعة');
    } catch (e) {
      _error.value = 'خطأ في تحميل أعضاء المجموعات: $e';
      debugPrint('خطأ في تحميل أعضاء المجموعات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على عضو مجموعة بالمعرف
  Future<void> getMemberById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final member = await _apiService.getMemberById(id);
      _currentMember.value = member;
      debugPrint('تم تحميل عضو المجموعة');
    } catch (e) {
      _error.value = 'خطأ في تحميل عضو المجموعة: $e';
      debugPrint('خطأ في تحميل عضو المجموعة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إضافة عضو إلى مجموعة
  Future<bool> addMemberToGroup(int groupId, int userId, String role) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final member = await _apiService.addMemberToGroup(groupId, userId, role);
      _allMembers.add(member);
      _applyFilters();
      debugPrint('تم إضافة عضو إلى المجموعة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إضافة عضو إلى المجموعة: $e';
      debugPrint('خطأ في إضافة عضو إلى المجموعة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث دور عضو في المجموعة
  Future<bool> updateMemberRole(int memberId, String newRole) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updateMemberRole(memberId, newRole);
      final index = _allMembers.indexWhere((m) => m.id == memberId);
      if (index != -1) {
        _allMembers[index] = _allMembers[index].copyWith(role: newRole);
        _applyFilters();
      }
      debugPrint('تم تحديث دور العضو في المجموعة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث دور العضو: $e';
      debugPrint('خطأ في تحديث دور العضو: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إزالة عضو من المجموعة
  Future<bool> removeMemberFromGroup(int memberId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.removeMemberFromGroup(memberId);
      _allMembers.removeWhere((m) => m.id == memberId);
      _applyFilters();
      debugPrint('تم إزالة العضو من المجموعة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إزالة العضو من المجموعة: $e';
      debugPrint('خطأ في إزالة العضو من المجموعة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على أعضاء مجموعة محددة
  Future<void> getMembersByGroup(int groupId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final members = await _apiService.getMembersByGroup(groupId);
      _groupMembers.assignAll(members);
      debugPrint('تم تحميل ${members.length} عضو للمجموعة $groupId');
    } catch (e) {
      _error.value = 'خطأ في تحميل أعضاء المجموعة: $e';
      debugPrint('خطأ في تحميل أعضاء المجموعة: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على مجموعات المستخدم
  Future<List<GroupMember>> getUserGroups(int userId) async {
    try {
      final groups = await _apiService.getUserGroups(userId);
      debugPrint('تم تحميل ${groups.length} مجموعة للمستخدم $userId');
      return groups;
    } catch (e) {
      debugPrint('خطأ في تحميل مجموعات المستخدم: $e');
      return [];
    }
  }

  /// تعيين عضو كمشرف
  Future<bool> promoteToAdmin(int memberId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.promoteToAdmin(memberId);
      final index = _allMembers.indexWhere((m) => m.id == memberId);
      if (index != -1) {
        _allMembers[index] = _allMembers[index].copyWith(role: 'admin');
        _applyFilters();
      }
      debugPrint('تم ترقية العضو إلى مشرف');
      return true;
    } catch (e) {
      _error.value = 'خطأ في ترقية العضو: $e';
      debugPrint('خطأ في ترقية العضو: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إزالة صلاحيات الإشراف من عضو
  Future<bool> demoteFromAdmin(int memberId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.demoteFromAdmin(memberId);
      final index = _allMembers.indexWhere((m) => m.id == memberId);
      if (index != -1) {
        _allMembers[index] = _allMembers[index].copyWith(role: 'member');
        _applyFilters();
      }
      debugPrint('تم إزالة صلاحيات الإشراف من العضو');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إزالة صلاحيات الإشراف: $e';
      debugPrint('خطأ في إزالة صلاحيات الإشراف: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// كتم عضو في المجموعة
  Future<bool> muteMember(int memberId, int durationMinutes) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.muteMember(memberId, durationMinutes);
      final index = _allMembers.indexWhere((m) => m.id == memberId);
      if (index != -1) {
        _allMembers[index] = _allMembers[index].copyWith(isMuted: true);
        _applyFilters();
      }
      debugPrint('تم كتم العضو في المجموعة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في كتم العضو: $e';
      debugPrint('خطأ في كتم العضو: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إلغاء كتم عضو في المجموعة
  Future<bool> unmuteMember(int memberId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.unmuteMember(memberId);
      final index = _allMembers.indexWhere((m) => m.id == memberId);
      if (index != -1) {
        _allMembers[index] = _allMembers[index].copyWith(isMuted: false);
        _applyFilters();
      }
      debugPrint('تم إلغاء كتم العضو في المجموعة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إلغاء كتم العضو: $e';
      debugPrint('خطأ في إلغاء كتم العضو: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allMembers.where((member) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!member.userName.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح المجموعة
      if (_groupFilter.value != null && member.groupId != _groupFilter.value) {
        return false;
      }

      // مرشح الدور
      if (_roleFilter.value != null && member.role != _roleFilter.value) {
        return false;
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && !member.isActive) {
        return false;
      }

      return true;
    }).toList();

    _filteredMembers.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح المجموعة
  void setGroupFilter(int? groupId) {
    _groupFilter.value = groupId;
    _applyFilters();
  }

  /// تعيين مرشح الدور
  void setRoleFilter(String? role) {
    _roleFilter.value = role;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _groupFilter.value = null;
    _roleFilter.value = null;
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllMembers();
  }
}
