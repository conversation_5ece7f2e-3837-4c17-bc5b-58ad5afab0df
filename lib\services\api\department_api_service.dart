import 'package:flutter/foundation.dart';
import '../../models/department_model.dart';
import 'api_service.dart';

/// خدمة API للأقسام - متطابقة مع ASP.NET Core API
class DepartmentApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع الأقسام
  Future<List<Department>> getAllDepartments() async {
    try {
      final response = await _apiService.get('/Departments');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل الأقسام: $e');
      rethrow;
    }
  }

  /// الحصول على قسم بواسطة المعرف
  Future<Department?> getDepartmentById(int id) async {
    try {
      final response = await _apiService.get('/Departments/$id');
      return _apiService.handleResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل القسم $id: $e');
      return null;
    }
  }

  /// الحصول على الأقسام النشطة فقط
  Future<List<Department>> getActiveDepartments() async {
    try {
      final response = await _apiService.get('/Departments/active');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل الأقسام النشطة: $e');
      return [];
    }
  }

  /// البحث في الأقسام
  Future<List<Department>> searchDepartments(String query) async {
    try {
      final response = await _apiService.get('/Departments/search?searchTerm=$query');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن الأقسام: $e');
      return [];
    }
  }

  /// إنشاء قسم جديد
  Future<Department?> createDepartment(Department department) async {
    try {
      final response = await _apiService.post(
        '/Departments',
        body: department.toJson(),
      );
      return _apiService.handleResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء القسم: $e');
      rethrow;
    }
  }

  /// تحديث قسم
  Future<Department?> updateDepartment(Department department) async {
    try {
      final response = await _apiService.put(
        '/Departments/${department.id}',
        body: department.toJson(),
      );
      return _apiService.handleResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث القسم ${department.id}: $e');
      rethrow;
    }
  }

  /// حذف قسم
  Future<bool> deleteDepartment(int id) async {
    try {
      final response = await _apiService.delete('/Departments/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف القسم $id: $e');
      return false;
    }
  }

  // تم إزالة الطرق غير المدعومة في API الحالي
  // يمكن إضافتها لاحقاً عند توفرها في ASP.NET Core API
}
