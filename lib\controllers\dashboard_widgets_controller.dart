import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/dashboard_models.dart';
import '../services/api/dashboard_widgets_api_service.dart';

/// متحكم عناصر لوحة المعلومات
class DashboardWidgetsController extends GetxController {
  final DashboardWidgetsApiService _apiService = DashboardWidgetsApiService();

  // قوائم العناصر
  final RxList<DashboardWidget> _allWidgets = <DashboardWidget>[].obs;
  final RxList<DashboardWidget> _filteredWidgets = <DashboardWidget>[].obs;
  final RxList<DashboardWidget> _dashboardWidgets = <DashboardWidget>[].obs;

  // العنصر الحالي
  final Rx<DashboardWidget?> _currentWidget = Rx<DashboardWidget?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<int?> _dashboardFilter = Rx<int?>(null);
  final Rx<String?> _typeFilter = Rx<String?>(null);
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<DashboardWidget> get allWidgets => _allWidgets;
  List<DashboardWidget> get filteredWidgets => _filteredWidgets;
  List<DashboardWidget> get dashboardWidgets => _dashboardWidgets;
  DashboardWidget? get currentWidget => _currentWidget.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  int? get dashboardFilter => _dashboardFilter.value;
  String? get typeFilter => _typeFilter.value;
  bool get showActiveOnly => _showActiveOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllWidgets();
  }

  /// تحميل جميع عناصر لوحة المعلومات
  Future<void> loadAllWidgets() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final widgets = await _apiService.getAllWidgets();
      _allWidgets.assignAll(widgets);
      _applyFilters();
      debugPrint('تم تحميل ${widgets.length} عنصر لوحة معلومات');
    } catch (e) {
      _error.value = 'خطأ في تحميل عناصر لوحة المعلومات: $e';
      debugPrint('خطأ في تحميل عناصر لوحة المعلومات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على عنصر لوحة معلومات بالمعرف
  Future<void> getWidgetById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final widget = await _apiService.getWidgetById(id);
      _currentWidget.value = widget;
      debugPrint('تم تحميل عنصر لوحة المعلومات: ${widget.title}');
    } catch (e) {
      _error.value = 'خطأ في تحميل عنصر لوحة المعلومات: $e';
      debugPrint('خطأ في تحميل عنصر لوحة المعلومات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء عنصر لوحة معلومات جديد
  Future<bool> createWidget(DashboardWidget widget) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newWidget = await _apiService.createWidget(widget);
      _allWidgets.add(newWidget);
      _applyFilters();
      debugPrint('تم إنشاء عنصر لوحة معلومات جديد: ${newWidget.title}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء عنصر لوحة المعلومات: $e';
      debugPrint('خطأ في إنشاء عنصر لوحة المعلومات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث عنصر لوحة معلومات
  Future<bool> updateWidget(int id, DashboardWidget widget) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updateWidget(id, widget);
      final index = _allWidgets.indexWhere((w) => w.id == id);
      if (index != -1) {
        _allWidgets[index] = widget;
        _applyFilters();
      }
      debugPrint('تم تحديث عنصر لوحة المعلومات: ${widget.title}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث عنصر لوحة المعلومات: $e';
      debugPrint('خطأ في تحديث عنصر لوحة المعلومات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف عنصر لوحة معلومات
  Future<bool> deleteWidget(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteWidget(id);
      _allWidgets.removeWhere((w) => w.id == id);
      _applyFilters();
      debugPrint('تم حذف عنصر لوحة المعلومات');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف عنصر لوحة المعلومات: $e';
      debugPrint('خطأ في حذف عنصر لوحة المعلومات: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على عناصر لوحة معلومات محددة
  Future<void> getWidgetsByDashboard(int dashboardId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final widgets = await _apiService.getWidgetsByDashboard(dashboardId);
      _dashboardWidgets.assignAll(widgets);
      debugPrint('تم تحميل ${widgets.length} عنصر للوحة المعلومات $dashboardId');
    } catch (e) {
      _error.value = 'خطأ في تحميل عناصر لوحة المعلومات: $e';
      debugPrint('خطأ في تحميل عناصر لوحة المعلومات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث موضع العنصر
  Future<bool> updateWidgetPosition(int widgetId, int x, int y, int width, int height) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updateWidgetPosition(widgetId, x, y, width, height);
      final index = _allWidgets.indexWhere((w) => w.id == widgetId);
      if (index != -1) {
        _allWidgets[index] = _allWidgets[index].copyWith(
          x: x,
          y: y,
          width: width,
          height: height,
        );
        _applyFilters();
      }
      debugPrint('تم تحديث موضع العنصر');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث موضع العنصر: $e';
      debugPrint('خطأ في تحديث موضع العنصر: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث إعدادات العنصر
  Future<bool> updateWidgetSettings(int widgetId, Map<String, dynamic> settings) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updateWidgetSettings(widgetId, settings);
      final index = _allWidgets.indexWhere((w) => w.id == widgetId);
      if (index != -1) {
        _allWidgets[index] = _allWidgets[index].copyWith(settings: settings);
        _applyFilters();
      }
      debugPrint('تم تحديث إعدادات العنصر');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث إعدادات العنصر: $e';
      debugPrint('خطأ في تحديث إعدادات العنصر: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث بيانات العنصر
  Future<Map<String, dynamic>?> refreshWidgetData(int widgetId) async {
    try {
      final data = await _apiService.refreshWidgetData(widgetId);
      debugPrint('تم تحديث بيانات العنصر');
      return data;
    } catch (e) {
      debugPrint('خطأ في تحديث بيانات العنصر: $e');
      return null;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allWidgets.where((widget) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!widget.title.toLowerCase().contains(query) &&
            !widget.description.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح لوحة المعلومات
      if (_dashboardFilter.value != null && widget.dashboardId != _dashboardFilter.value) {
        return false;
      }

      // مرشح النوع
      if (_typeFilter.value != null && widget.type != _typeFilter.value) {
        return false;
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && !widget.isActive) {
        return false;
      }

      return true;
    }).toList();

    _filteredWidgets.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح لوحة المعلومات
  void setDashboardFilter(int? dashboardId) {
    _dashboardFilter.value = dashboardId;
    _applyFilters();
  }

  /// تعيين مرشح النوع
  void setTypeFilter(String? type) {
    _typeFilter.value = type;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _dashboardFilter.value = null;
    _typeFilter.value = null;
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllWidgets();
  }
}
