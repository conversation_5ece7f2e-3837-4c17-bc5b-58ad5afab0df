import 'package:flutter/foundation.dart';
import '../../models/archive_models.dart';
import 'api_service.dart';

/// خدمة API لفئات الأرشيف - متطابقة مع ASP.NET Core API
class ArchiveCategoriesApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع فئات الأرشيف
  Future<List<ArchiveCategory>> getAllCategories() async {
    try {
      final response = await _apiService.get('/ArchiveCategories');
      return _apiService.handleListResponse<ArchiveCategory>(
        response,
        (json) => ArchiveCategory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على فئات الأرشيف: $e');
      rethrow;
    }
  }

  /// الحصول على فئة أرشيف بواسطة المعرف
  Future<ArchiveCategory?> getCategoryById(int id) async {
    try {
      final response = await _apiService.get('/ArchiveCategories/$id');
      return _apiService.handleResponse<ArchiveCategory>(
        response,
        (json) => ArchiveCategory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على فئة الأرشيف: $e');
      return null;
    }
  }

  /// إنشاء فئة أرشيف جديدة
  Future<ArchiveCategory?> createCategory(ArchiveCategory category) async {
    try {
      final response = await _apiService.post(
        '/ArchiveCategories',
        body: category.toJson(),
      );
      return _apiService.handleResponse<ArchiveCategory>(
        response,
        (json) => ArchiveCategory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء فئة الأرشيف: $e');
      rethrow;
    }
  }

  /// تحديث فئة أرشيف
  Future<ArchiveCategory?> updateCategory(ArchiveCategory category) async {
    try {
      final response = await _apiService.put(
        '/ArchiveCategories/${category.id}',
        body: category.toJson(),
      );
      return _apiService.handleResponse<ArchiveCategory>(
        response,
        (json) => ArchiveCategory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث فئة الأرشيف: $e');
      rethrow;
    }
  }

  /// حذف فئة أرشيف
  Future<bool> deleteCategory(int id) async {
    try {
      final response = await _apiService.delete('/ArchiveCategories/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف فئة الأرشيف: $e');
      return false;
    }
  }

  /// الحصول على الفئات النشطة
  Future<List<ArchiveCategory>> getActiveCategories() async {
    try {
      final response = await _apiService.get('/ArchiveCategories/active');
      return _apiService.handleListResponse<ArchiveCategory>(
        response,
        (json) => ArchiveCategory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الفئات النشطة: $e');
      rethrow;
    }
  }

  /// البحث في فئات الأرشيف
  Future<List<ArchiveCategory>> searchCategories(String query) async {
    try {
      final response = await _apiService.get('/ArchiveCategories/search?searchTerm=$query');
      return _apiService.handleListResponse<ArchiveCategory>(
        response,
        (json) => ArchiveCategory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن فئات الأرشيف: $e');
      rethrow;
    }
  }

  /// الحصول على إحصائيات الفئات
  Future<Map<String, dynamic>?> getCategoryStatistics() async {
    try {
      final response = await _apiService.get('/ArchiveCategories/statistics');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return Map<String, dynamic>.from(response.body as Map);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات الفئات: $e');
      return null;
    }
  }

  /// تحديث حالة نشاط الفئة
  Future<bool> updateCategoryActiveStatus(int categoryId, bool isActive) async {
    try {
      final response = await _apiService.put(
        '/ArchiveCategories/$categoryId/active',
        body: {'isActive': isActive},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة نشاط الفئة: $e');
      return false;
    }
  }

  /// الحصول على المستندات في فئة معينة
  Future<List<Map<String, dynamic>>> getCategoryDocuments(int categoryId) async {
    try {
      final response = await _apiService.get('/ArchiveCategories/$categoryId/documents');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return List<Map<String, dynamic>>.from(response.body as List);
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على مستندات الفئة: $e');
      return [];
    }
  }

  /// نقل مستندات من فئة إلى أخرى
  Future<bool> moveDocumentsToCategory(List<int> documentIds, int newCategoryId) async {
    try {
      final response = await _apiService.put(
        '/ArchiveCategories/move-documents',
        body: {
          'documentIds': documentIds,
          'newCategoryId': newCategoryId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في نقل المستندات: $e');
      return false;
    }
  }

  /// دمج فئتين
  Future<bool> mergeCategories(int sourceCategoryId, int targetCategoryId) async {
    try {
      final response = await _apiService.post(
        '/ArchiveCategories/merge',
        body: {
          'sourceCategoryId': sourceCategoryId,
          'targetCategoryId': targetCategoryId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في دمج الفئات: $e');
      return false;
    }
  }

  /// الحصول على الفئات الفرعية
  Future<List<ArchiveCategory>> getSubCategories(int parentCategoryId) async {
    try {
      final response = await _apiService.get('/ArchiveCategories/$parentCategoryId/subcategories');
      return _apiService.handleListResponse<ArchiveCategory>(
        response,
        (json) => ArchiveCategory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الفئات الفرعية: $e');
      rethrow;
    }
  }

  /// إنشاء فئة فرعية
  Future<ArchiveCategory?> createSubCategory(int parentCategoryId, ArchiveCategory category) async {
    try {
      final response = await _apiService.post(
        '/ArchiveCategories/$parentCategoryId/subcategories',
        body: category.toJson(),
      );
      return _apiService.handleResponse<ArchiveCategory>(
        response,
        (json) => ArchiveCategory.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء الفئة الفرعية: $e');
      rethrow;
    }
  }

  /// تصدير فئات الأرشيف
  Future<String?> exportCategories(String format) async {
    try {
      final response = await _apiService.get('/ArchiveCategories/export?format=$format');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تصدير فئات الأرشيف: $e');
      return null;
    }
  }

  /// استيراد فئات الأرشيف
  Future<bool> importCategories(String data, String format) async {
    try {
      final response = await _apiService.post(
        '/ArchiveCategories/import',
        body: {
          'data': data,
          'format': format,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في استيراد فئات الأرشيف: $e');
      return false;
    }
  }
}
