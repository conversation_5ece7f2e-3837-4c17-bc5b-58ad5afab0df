import 'package:flutter/foundation.dart';
import '../../models/notification_models.dart';
import 'api_service.dart';

/// خدمة API لإعدادات الإشعارات - متطابقة مع ASP.NET Core API
class NotificationSettingsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع إعدادات الإشعارات
  Future<List<NotificationSettingModel>> getAllSettings() async {
    try {
      final response = await _apiService.get('/NotificationSettings');
      return _apiService.handleListResponse<NotificationSettingModel>(
        response,
        (json) => NotificationSettingModel.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إعدادات الإشعارات: $e');
      rethrow;
    }
  }

  /// الحصول على إعداد إشعار بواسطة المعرف
  Future<NotificationSettingModel?> getSettingById(int id) async {
    try {
      final response = await _apiService.get('/NotificationSettings/$id');
      return _apiService.handleResponse<NotificationSettingModel>(
        response,
        (json) => NotificationSettingModel.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إعداد الإشعار $id: $e');
      return null;
    }
  }

  /// الحصول على إعدادات الإشعارات لمستخدم محدد
  Future<List<NotificationSettingModel>> getSettingsByUserId(int userId) async {
    try {
      final response = await _apiService.get('/NotificationSettings/user/$userId');
      return _apiService.handleListResponse<NotificationSettingModel>(
        response,
        (json) => NotificationSettingModel.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إعدادات المستخدم $userId: $e');
      rethrow;
    }
  }

  /// الحصول على إعدادات الإشعارات للمستخدم الحالي
  Future<List<NotificationSettingModel>> getCurrentUserSettings() async {
    try {
      final response = await _apiService.get('/NotificationSettings/current-user');
      return _apiService.handleListResponse<NotificationSettingModel>(
        response,
        (json) => NotificationSettingModel.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إعدادات المستخدم الحالي: $e');
      rethrow;
    }
  }

  /// إنشاء إعداد إشعار جديد
  Future<NotificationSettingModel> createSetting(NotificationSettingModel setting) async {
    try {
      final response = await _apiService.post(
        '/NotificationSettings',
        body: setting.toJson(),
      );
      return _apiService.handleResponse<NotificationSettingModel>(
        response,
        (json) => NotificationSettingModel.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء إعداد الإشعار: $e');
      rethrow;
    }
  }

  /// تحديث إعداد إشعار
  Future<NotificationSettingModel> updateSetting(int id, NotificationSettingModel setting) async {
    try {
      final response = await _apiService.put(
        '/NotificationSettings/$id',
        body: setting.toJson(),
      );
      return _apiService.handleResponse<NotificationSettingModel>(
        response,
        (json) => NotificationSettingModel.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث إعداد الإشعار $id: $e');
      rethrow;
    }
  }

  /// حذف إعداد إشعار
  Future<bool> deleteSetting(int id) async {
    try {
      final response = await _apiService.delete('/NotificationSettings/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف إعداد الإشعار $id: $e');
      return false;
    }
  }

  /// تحديث إعدادات متعددة للمستخدم الحالي
  Future<bool> updateCurrentUserSettings(List<NotificationSettingModel> settings) async {
    try {
      final response = await _apiService.put(
        '/NotificationSettings/current-user/bulk-update',
        body: {
          'settings': settings.map((s) => s.toJson()).toList(),
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث إعدادات المستخدم الحالي: $e');
      return false;
    }
  }

  /// تفعيل/إلغاء تفعيل نوع إشعار محدد للمستخدم الحالي
  Future<bool> toggleNotificationType(String notificationType, bool isEnabled) async {
    try {
      final response = await _apiService.put(
        '/NotificationSettings/current-user/toggle',
        body: {
          'notificationType': notificationType,
          'isEnabled': isEnabled,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تغيير حالة نوع الإشعار: $e');
      return false;
    }
  }

  /// تفعيل/إلغاء تفعيل الإشعارات عبر البريد الإلكتروني
  Future<bool> toggleEmailNotifications(bool isEnabled) async {
    try {
      final response = await _apiService.put(
        '/NotificationSettings/current-user/toggle-email',
        body: {
          'isEmailEnabled': isEnabled,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تغيير حالة الإشعارات عبر البريد الإلكتروني: $e');
      return false;
    }
  }

  /// تفعيل/إلغاء تفعيل الإشعارات المدفوعة (Push)
  Future<bool> togglePushNotifications(bool isEnabled) async {
    try {
      final response = await _apiService.put(
        '/NotificationSettings/current-user/toggle-push',
        body: {
          'isPushEnabled': isEnabled,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تغيير حالة الإشعارات المدفوعة: $e');
      return false;
    }
  }

  /// تفعيل/إلغاء تفعيل الإشعارات عبر الرسائل النصية
  Future<bool> toggleSmsNotifications(bool isEnabled) async {
    try {
      final response = await _apiService.put(
        '/NotificationSettings/current-user/toggle-sms',
        body: {
          'isSmsEnabled': isEnabled,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تغيير حالة الإشعارات عبر الرسائل النصية: $e');
      return false;
    }
  }

  /// إعادة تعيين إعدادات الإشعارات للقيم الافتراضية
  Future<bool> resetToDefaults() async {
    try {
      final response = await _apiService.post(
        '/NotificationSettings/current-user/reset-defaults',
        body: {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين الإعدادات للقيم الافتراضية: $e');
      return false;
    }
  }

  /// الحصول على أنواع الإشعارات المتاحة
  Future<List<String>> getAvailableNotificationTypes() async {
    try {
      final response = await _apiService.get('/NotificationSettings/available-types');
      final data = _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
      return List<String>.from(data['types'] ?? []);
    } catch (e) {
      debugPrint('خطأ في الحصول على أنواع الإشعارات المتاحة: $e');
      return [];
    }
  }
}
