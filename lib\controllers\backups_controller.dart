import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/system_models.dart';
import '../services/api/backups_api_service.dart';

/// متحكم النسخ الاحتياطية
class BackupsController extends GetxController {
  final BackupsApiService _apiService = BackupsApiService();

  // قوائم النسخ الاحتياطية
  final RxList<Backup> _allBackups = <Backup>[].obs;
  final RxList<Backup> _filteredBackups = <Backup>[].obs;

  // النسخة الاحتياطية الحالية
  final Rx<Backup?> _currentBackup = Rx<Backup?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  final RxString _successMessage = ''.obs;

  // حالة العمليات
  final RxBool _isCreatingBackup = false.obs;
  final RxBool _isRestoringBackup = false.obs;
  final RxBool _isDeletingBackup = false.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<String?> _typeFilter = Rx<String?>(null);
  final Rx<DateTime?> _dateFilter = Rx<DateTime?>(null);

  // Getters
  List<Backup> get allBackups => _allBackups;
  List<Backup> get filteredBackups => _filteredBackups;
  Backup? get currentBackup => _currentBackup.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get successMessage => _successMessage.value;
  bool get isCreatingBackup => _isCreatingBackup.value;
  bool get isRestoringBackup => _isRestoringBackup.value;
  bool get isDeletingBackup => _isDeletingBackup.value;
  String get searchQuery => _searchQuery.value;
  String? get typeFilter => _typeFilter.value;
  DateTime? get dateFilter => _dateFilter.value;

  @override
  void onInit() {
    super.onInit();
    loadAllBackups();
  }

  /// تحميل جميع النسخ الاحتياطية
  Future<void> loadAllBackups() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final backups = await _apiService.getAllBackups();
      _allBackups.assignAll(backups);
      _applyFilters();
      debugPrint('تم تحميل ${backups.length} نسخة احتياطية');
    } catch (e) {
      _error.value = 'خطأ في تحميل النسخ الاحتياطية: $e';
      debugPrint('خطأ في تحميل النسخ الاحتياطية: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على نسخة احتياطية بالمعرف
  Future<void> getBackupById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final backup = await _apiService.getBackupById(id);
      _currentBackup.value = backup;
      debugPrint('تم تحميل النسخة الاحتياطية: ${backup.name}');
    } catch (e) {
      _error.value = 'خطأ في تحميل النسخة الاحتياطية: $e';
      debugPrint('خطأ في تحميل النسخة الاحتياطية: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء نسخة احتياطية جديدة
  Future<bool> createBackup(String name, String description, String type) async {
    _isCreatingBackup.value = true;
    _error.value = '';
    _successMessage.value = '';

    try {
      final backup = await _apiService.createBackup(name, description, type);
      _allBackups.insert(0, backup);
      _applyFilters();
      _successMessage.value = 'تم إنشاء النسخة الاحتياطية بنجاح';
      debugPrint('تم إنشاء نسخة احتياطية جديدة: ${backup.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء النسخة الاحتياطية: $e';
      debugPrint('خطأ في إنشاء النسخة الاحتياطية: $e');
      return false;
    } finally {
      _isCreatingBackup.value = false;
    }
  }

  /// استعادة نسخة احتياطية
  Future<bool> restoreBackup(int backupId) async {
    _isRestoringBackup.value = true;
    _error.value = '';
    _successMessage.value = '';

    try {
      await _apiService.restoreBackup(backupId);
      _successMessage.value = 'تم استعادة النسخة الاحتياطية بنجاح';
      debugPrint('تم استعادة النسخة الاحتياطية');
      return true;
    } catch (e) {
      _error.value = 'خطأ في استعادة النسخة الاحتياطية: $e';
      debugPrint('خطأ في استعادة النسخة الاحتياطية: $e');
      return false;
    } finally {
      _isRestoringBackup.value = false;
    }
  }

  /// حذف نسخة احتياطية
  Future<bool> deleteBackup(int id) async {
    _isDeletingBackup.value = true;
    _error.value = '';

    try {
      await _apiService.deleteBackup(id);
      _allBackups.removeWhere((b) => b.id == id);
      _applyFilters();
      _successMessage.value = 'تم حذف النسخة الاحتياطية بنجاح';
      debugPrint('تم حذف النسخة الاحتياطية');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف النسخة الاحتياطية: $e';
      debugPrint('خطأ في حذف النسخة الاحتياطية: $e');
      return false;
    } finally {
      _isDeletingBackup.value = false;
    }
  }

  /// تنزيل نسخة احتياطية
  Future<String?> downloadBackup(int backupId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final filePath = await _apiService.downloadBackup(backupId);
      _successMessage.value = 'تم تنزيل النسخة الاحتياطية بنجاح';
      debugPrint('تم تنزيل النسخة الاحتياطية: $filePath');
      return filePath;
    } catch (e) {
      _error.value = 'خطأ في تنزيل النسخة الاحتياطية: $e';
      debugPrint('خطأ في تنزيل النسخة الاحتياطية: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// التحقق من حالة النسخة الاحتياطية
  Future<String?> getBackupStatus(int backupId) async {
    try {
      final status = await _apiService.getBackupStatus(backupId);
      debugPrint('حالة النسخة الاحتياطية: $status');
      return status;
    } catch (e) {
      debugPrint('خطأ في التحقق من حالة النسخة الاحتياطية: $e');
      return null;
    }
  }

  /// جدولة نسخة احتياطية تلقائية
  Future<bool> scheduleAutoBackup(String schedule, String type) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.scheduleAutoBackup(schedule, type);
      _successMessage.value = 'تم جدولة النسخ الاحتياطية التلقائية بنجاح';
      debugPrint('تم جدولة النسخ الاحتياطية التلقائية');
      return true;
    } catch (e) {
      _error.value = 'خطأ في جدولة النسخ الاحتياطية التلقائية: $e';
      debugPrint('خطأ في جدولة النسخ الاحتياطية التلقائية: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allBackups.where((backup) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!backup.name.toLowerCase().contains(query) &&
            !backup.description.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح النوع
      if (_typeFilter.value != null && backup.type != _typeFilter.value) {
        return false;
      }

      // مرشح التاريخ
      if (_dateFilter.value != null) {
        final backupDate = DateTime.fromMillisecondsSinceEpoch(backup.createdAt * 1000);
        final filterDate = _dateFilter.value!;
        if (backupDate.year != filterDate.year ||
            backupDate.month != filterDate.month ||
            backupDate.day != filterDate.day) {
          return false;
        }
      }

      return true;
    }).toList();

    _filteredBackups.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح النوع
  void setTypeFilter(String? type) {
    _typeFilter.value = type;
    _applyFilters();
  }

  /// تعيين مرشح التاريخ
  void setDateFilter(DateTime? date) {
    _dateFilter.value = date;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _typeFilter.value = null;
    _dateFilter.value = null;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// مسح رسالة النجاح
  void clearSuccessMessage() {
    _successMessage.value = '';
  }

  /// مسح جميع الرسائل
  void clearMessages() {
    _error.value = '';
    _successMessage.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllBackups();
  }
}
