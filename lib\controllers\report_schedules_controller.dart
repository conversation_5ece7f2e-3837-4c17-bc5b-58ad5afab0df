import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/report_models.dart';
import '../services/api/report_schedules_api_service.dart';

/// متحكم جداول التقارير
class ReportSchedulesController extends GetxController {
  final ReportSchedulesApiService _apiService = ReportSchedulesApiService();

  // قوائم الجداول
  final RxList<ReportSchedule> _allSchedules = <ReportSchedule>[].obs;
  final RxList<ReportSchedule> _filteredSchedules = <ReportSchedule>[].obs;
  final RxList<ReportSchedule> _activeSchedules = <ReportSchedule>[].obs;

  // الجدول الحالي
  final Rx<ReportSchedule?> _currentSchedule = Rx<ReportSchedule?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<String?> _frequencyFilter = Rx<String?>(null);
  final Rx<String?> _statusFilter = Rx<String?>(null);
  final RxBool _showActiveOnly = true.obs;

  // إحصائيات
  final RxInt _totalSchedules = 0.obs;
  final RxInt _activeSchedulesCount = 0.obs;
  final RxInt _pausedSchedulesCount = 0.obs;

  // Getters
  List<ReportSchedule> get allSchedules => _allSchedules;
  List<ReportSchedule> get filteredSchedules => _filteredSchedules;
  List<ReportSchedule> get activeSchedules => _activeSchedules;
  ReportSchedule? get currentSchedule => _currentSchedule.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  String? get frequencyFilter => _frequencyFilter.value;
  String? get statusFilter => _statusFilter.value;
  bool get showActiveOnly => _showActiveOnly.value;
  int get totalSchedules => _totalSchedules.value;
  int get activeSchedulesCount => _activeSchedulesCount.value;
  int get pausedSchedulesCount => _pausedSchedulesCount.value;

  @override
  void onInit() {
    super.onInit();
    loadAllSchedules();
    loadActiveSchedules();
    loadStatistics();
  }

  /// تحميل جميع جداول التقارير
  Future<void> loadAllSchedules() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final schedules = await _apiService.getAllSchedules();
      _allSchedules.assignAll(schedules);
      _applyFilters();
      debugPrint('تم تحميل ${schedules.length} جدول تقرير');
    } catch (e) {
      _error.value = 'خطأ في تحميل جداول التقارير: $e';
      debugPrint('خطأ في تحميل جداول التقارير: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل الجداول النشطة
  Future<void> loadActiveSchedules() async {
    try {
      final schedules = await _apiService.getActiveSchedules();
      _activeSchedules.assignAll(schedules);
      debugPrint('تم تحميل ${schedules.length} جدول نشط');
    } catch (e) {
      debugPrint('خطأ في تحميل الجداول النشطة: $e');
    }
  }

  /// تحميل الإحصائيات
  Future<void> loadStatistics() async {
    try {
      final stats = await _apiService.getStatistics();
      _totalSchedules.value = stats['total'] ?? 0;
      _activeSchedulesCount.value = stats['active'] ?? 0;
      _pausedSchedulesCount.value = stats['paused'] ?? 0;
      debugPrint('تم تحميل إحصائيات جداول التقارير');
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات جداول التقارير: $e');
    }
  }

  /// الحصول على جدول تقرير بالمعرف
  Future<void> getScheduleById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final schedule = await _apiService.getScheduleById(id);
      _currentSchedule.value = schedule;
      debugPrint('تم تحميل جدول التقرير: ${schedule.name}');
    } catch (e) {
      _error.value = 'خطأ في تحميل جدول التقرير: $e';
      debugPrint('خطأ في تحميل جدول التقرير: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء جدول تقرير جديد
  Future<bool> createSchedule(ReportSchedule schedule) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newSchedule = await _apiService.createSchedule(schedule);
      _allSchedules.add(newSchedule);
      _applyFilters();
      await loadStatistics();
      debugPrint('تم إنشاء جدول تقرير جديد: ${newSchedule.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء جدول التقرير: $e';
      debugPrint('خطأ في إنشاء جدول التقرير: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث جدول تقرير
  Future<bool> updateSchedule(int id, ReportSchedule schedule) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updateSchedule(id, schedule);
      final index = _allSchedules.indexWhere((s) => s.id == id);
      if (index != -1) {
        _allSchedules[index] = schedule;
        _applyFilters();
      }
      debugPrint('تم تحديث جدول التقرير: ${schedule.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث جدول التقرير: $e';
      debugPrint('خطأ في تحديث جدول التقرير: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف جدول تقرير
  Future<bool> deleteSchedule(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteSchedule(id);
      _allSchedules.removeWhere((s) => s.id == id);
      _applyFilters();
      await loadStatistics();
      debugPrint('تم حذف جدول التقرير');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف جدول التقرير: $e';
      debugPrint('خطأ في حذف جدول التقرير: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تشغيل جدول تقرير
  Future<bool> startSchedule(int scheduleId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.startSchedule(scheduleId);
      final index = _allSchedules.indexWhere((s) => s.id == scheduleId);
      if (index != -1) {
        _allSchedules[index] = _allSchedules[index].copyWith(status: 'active');
        _applyFilters();
      }
      await loadActiveSchedules();
      await loadStatistics();
      debugPrint('تم تشغيل جدول التقرير');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تشغيل جدول التقرير: $e';
      debugPrint('خطأ في تشغيل جدول التقرير: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إيقاف جدول تقرير
  Future<bool> pauseSchedule(int scheduleId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.pauseSchedule(scheduleId);
      final index = _allSchedules.indexWhere((s) => s.id == scheduleId);
      if (index != -1) {
        _allSchedules[index] = _allSchedules[index].copyWith(status: 'paused');
        _applyFilters();
      }
      await loadActiveSchedules();
      await loadStatistics();
      debugPrint('تم إيقاف جدول التقرير');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إيقاف جدول التقرير: $e';
      debugPrint('خطأ في إيقاف جدول التقرير: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تشغيل جدول تقرير فوراً
  Future<bool> runScheduleNow(int scheduleId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.runScheduleNow(scheduleId);
      debugPrint('تم تشغيل جدول التقرير فوراً');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تشغيل جدول التقرير فوراً: $e';
      debugPrint('خطأ في تشغيل جدول التقرير فوراً: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على تاريخ التشغيل التالي
  Future<DateTime?> getNextRunTime(int scheduleId) async {
    try {
      final nextRun = await _apiService.getNextRunTime(scheduleId);
      debugPrint('تاريخ التشغيل التالي: $nextRun');
      return nextRun;
    } catch (e) {
      debugPrint('خطأ في الحصول على تاريخ التشغيل التالي: $e');
      return null;
    }
  }

  /// الحصول على سجل تشغيل الجدول
  Future<List<Map<String, dynamic>>?> getScheduleHistory(int scheduleId) async {
    try {
      final history = await _apiService.getScheduleHistory(scheduleId);
      debugPrint('تم تحميل سجل تشغيل الجدول');
      return history;
    } catch (e) {
      debugPrint('خطأ في تحميل سجل تشغيل الجدول: $e');
      return null;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allSchedules.where((schedule) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!schedule.name.toLowerCase().contains(query) &&
            !schedule.description.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح التكرار
      if (_frequencyFilter.value != null && schedule.frequency != _frequencyFilter.value) {
        return false;
      }

      // مرشح الحالة
      if (_statusFilter.value != null && schedule.status != _statusFilter.value) {
        return false;
      }

      // مرشح النشط فقط
      if (_showActiveOnly.value && schedule.status != 'active') {
        return false;
      }

      return true;
    }).toList();

    _filteredSchedules.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح التكرار
  void setFrequencyFilter(String? frequency) {
    _frequencyFilter.value = frequency;
    _applyFilters();
  }

  /// تعيين مرشح الحالة
  void setStatusFilter(String? status) {
    _statusFilter.value = status;
    _applyFilters();
  }

  /// تعيين مرشح النشط فقط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _frequencyFilter.value = null;
    _statusFilter.value = null;
    _showActiveOnly.value = true;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await Future.wait([
      loadAllSchedules(),
      loadActiveSchedules(),
      loadStatistics(),
    ]);
  }
}
